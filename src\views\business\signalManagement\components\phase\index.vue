<template>
  <div class="phase-container" ref="phase-container">
    <el-button style="margin-bottom: 10px" type="primary" @click="onAdd">添加</el-button>
    <el-button style="margin-bottom: 10px" type="primary" @click="deleteAllData">全部删除</el-button>
    <el-button style="margin-bottom: 10px" type="primary" @click="handleNumber">添加编码</el-button>
    <el-button style="margin-bottom: 10px; float: right" link @click="stageChangeRing">阶段转环配置</el-button>
    <el-dialog width="700px" v-model="boxVisible" :title="modalTitle" :close-on-click-modal="false" @close="oncancle" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="有效相位">
          <el-select multiple v-model="form.phase" @change="handlePhaseChange" placeholder="请选择" size="small" clearable style="width: 200px">
            <el-option v-for="item in phaseOptions" :key="`${item.value}-${item.label}`" :label="getLable(item.label)" :value="item.value">
              <div class="single-model">
                <XRDDirSelector Width="60px" Height="60px" :showlist="item.item" />
                <span style="float: right; margin-left: 10px; color: #8492a6; font-size: 13px">{{ item.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-table
        class="tb-edit"
        ref="singleTables"
        row-key="id"
        :data="tableList"
        v-loading.body="listLoading"
        element-loading-text="Loading"
        fit
        highlight-current-row
        v-clickoutsides="cancelTables"
        :max-height="760"
        id="footerBtn"
      >
        <el-table-column prop="id" sortable align="center" label="ID" min-width="60">
          <template v-slot="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column class="table-column" label="车道方向" min-width="150" align="center">
          <template v-slot="scope">
            <Tankuang
              :disabled="true"
              :list="scope.row.direction"
              :imgs="imgs"
              :index="scope.$index"
              :showBottomName="showBottomName"
              :lines="lines"
              :rows="rows"
              :refresh="refreshTankuang"
              :showSpan="false"
              :showDirIcon="true"
              @finsh="handlefinsh"
            />
          </template>
        </el-table-column>
        <el-table-column class="table-column" label="行人方向" min-width="150" align="center">
          <template v-slot="scope">
            <PedTankuang
              :disabled="true"
              :list="scope.row.peddirection"
              :imgs="pedimgs"
              :index="scope.$index"
              :showBottomName="showBottomName"
              :lines="lines"
              :rows="rows"
              :refresh="refreshTankuang"
              :showSpan="false"
              :showDirIcon="true"
              @finsh="handlefinshped"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="环" min-width="100">
          <template v-slot="scope">
            <el-input-number
              size="small"
              controls-position="right"
              :min="1"
              :max="4"
              :step="1"
              :precision="0"
              v-model.number="scope.row.newring"
              style="width: 100px"
            />
            <span>{{ scope.row.newring }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="并发相位" min-width="120">
          <template v-slot="scope">
            <el-select
              multiple
              :value="scope.row.newconcurrent"
              @visible-change="getStageChangeRingConcurrent(scope.row, $event)"
              size="small"
              @change="associateStageChangeRingConcurrent(scope.row, $event)"
            >
              <el-option v-for="item in StageChangeRingConcurrentList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <span v-text="getConcurrentstrs(scope.row)"></span>
          </template>
        </el-table-column>
      </el-table>
      <el-button style="margin-top: 10px" type="primary" class="btn canclebtn" @click="barrier">生成屏障</el-button>
      <div style="font-size: 24px; margin-top: 20px" v-for="(ringData, index) in formattedRings" :key="index">
        <strong>{{ ringData.ringLabel }}:</strong> {{ ringData.values + '|' }}
      </div>
      <template v-slot:footer>
        <div class="boxFooter">
          <el-button class="btn canclebtn" @click="oncancle">取消</el-button>
          <el-button class="btn okbtn" type="primary" @click="onok">保存</el-button>
        </div>
      </template>
    </el-dialog>
    <el-table
      @row-click="handleRowClick"
      class="tb-edit"
      ref="singleTable"
      row-key="id"
      :data="tscParam.phaseList"
      v-loading.body="listLoading"
      element-loading-text="Loading"
      fit
      highlight-current-row
      v-clickoutside="cancelTable"
      :max-height="tableHeight"
      id="footerBtn"
    >
      <el-table-column prop="id" sortable align="center" label="ID" min-width="60">
        <template #default="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column class="table-column" label="车道方向" min-width="150" align="center">
        <template #default="scope">
          <Tankuang
            :list="scope.row.direction"
            :imgs="imgs"
            :index="scope.$index"
            :showBottomName="showBottomName"
            :lines="lines"
            :rows="rows"
            :refresh="refreshTankuang"
            :showSpan="false"
            :showDirIcon="true"
            :disabled="false"
            :laneIconStyle="{ left: '60px', top: '10px' }"
            :pedIconStyle="{ left: '60px', top: '10px' }"
            @finsh="handlefinsh"
          />
        </template>
      </el-table-column>
      <el-table-column class="table-column" label="行人方向" min-width="150" align="center">
        <template #default="scope">
          <PedTankuang
            :list="scope.row.peddirection"
            :imgs="pedimgs"
            :index="scope.$index"
            :showBottomName="showBottomName"
            :lines="lines"
            :rows="rows"
            :refresh="refreshTankuang"
            :showSpan="false"
            :showDirIcon="true"
            :disabled="false"
            :laneIconStyle="{ left: '60px', top: '10px' }"
            :pedIconStyle="{ left: '60px', top: '15px' }"
            @finsh="handlefinshped"
          />
        </template>
      </el-table-column>
      <el-table-column prop="controltype" class="table-column" label="控制类型" min-width="150" align="center">
        <template #default="scope">
          <el-select v-model="scope.row.controltype" size="small">
            <el-option v-for="item in controlTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <span v-text="getControlTypestr(scope.row.controltype)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="环" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="1"
            :max="4"
            :step="1"
            :precision="0"
            v-model.number="scope.row.ring"
            @change="handleRingEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.ring }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="并发相位" min-width="120">
        <template #default="scope">
          <el-select
            multiple
            v-model="scope.row.concurrent"
            @visible-change="getPhaseListConcurrent(scope.row, $event)"
            size="small"
            @change="associateConcurrent(scope.row, $event)"
          >
            <el-option
              v-for="item in ConcurrentList"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
          <span v-text="getConcurrentstr(scope.row)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="最小绿" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.mingreen"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.mingreen }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="最大绿1" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="65535"
            :step="1"
            :precision="0"
            v-model.number="scope.row.max1"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.max1 }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="最大绿2" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="65535"
            :step="1"
            :precision="0"
            v-model.number="scope.row.max2"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.max2 }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="status-col" label="延长绿" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="25"
            :step="1"
            :precision="0"
            v-model.number="scope.row.passage"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.passage }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="绿闪" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.flashgreen"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.flashgreen }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="黄灯" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="25"
            :step="1"
            :precision="0"
            v-model.number="scope.row.yellow"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.yellow }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="红灯清空" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="25"
            :step="1"
            :precision="0"
            v-model.number="scope.row.redclear"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.redclear }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="status-col" label="红黄" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.redyellow"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.redyellow }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="status-col" label="行人过街" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="25"
            :step="1"
            :precision="0"
            v-model.number="scope.row.phasewalk"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.phasewalk }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="pedclear" label="行人清空" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.pedclear"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.pedclear }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="gapout" label="浪费绿" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.gapout"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.gapout }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="greenpulse" label="绿脉冲倒计时" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.greenpulse"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.greenpulse }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="redpulse" label="红脉冲倒计时" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.redpulse"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.redpulse }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="pedgreenpulse" label="行人绿灯脉冲倒计时" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.pedgreenpulse"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.pedgreenpulse }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="vehiclethresh" label="车辆排队阈值" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.vehiclethresh"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.vehiclethresh }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="pedestrianthresh" label="行人等待阈值" min-width="100">
        <template #default="scope">
          <el-input-number
            size="small"
            controls-position="right"
            :min="0"
            :max="255"
            :step="1"
            :precision="0"
            v-model.number="scope.row.pedestrianthresh"
            @change="handleEdit(scope.$index, scope.row)"
            style="width: 100px"
          ></el-input-number>
          <span>{{ scope.row.pedestrianthresh }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="脉冲屏蔽" min-width="100">
        <template #default="scope">
          <el-select v-model="scope.row.pulsetype" placeholder="请选择" size="small">
            <el-option v-for="item in pulseTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <span>{{ getPulsetypestr(scope.row.pulsetype) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="关联相位" min-width="180">
        <template #default="scope">
          <el-select v-el-select-loadmore="loadMore" @focus="changeLink" v-model="scope.row.linkphase" placeholder="请选择" size="small" clearable>
            <el-option v-for="item in Options.slice(0, range)" :key="`${item.value}-${item.label}`" :label="item.label" :value="item.value">
              <div class="single-model">
                <XRDDirSelector Width="60px" Height="60px" :showlist="item.item"></XRDDirSelector>
                <span style="float: right; margin-left: 10px; color: #8492a6; font-size: 13px">{{ item.label }}</span>
              </div>
            </el-option>
          </el-select>
          <span v-if="scope.row.linkphase">
            <XRDDirSelector :Data="Data" :Datas="Datas" Width="60px" Height="60px" :showlist="showIcon(scope.row.linkphase)"></XRDDirSelector>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="140">
        <template #default="scope">
          <el-button link @click="handleClone(scope.$index, scope.row)">克隆</el-button>
          <el-button link @click="handleDelete(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, computed, onMounted, nextTick, getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import XRDDirSelector from '@/components/XRDDirSelector';
import Tankuang from '@/components/Tankuang';
import PedTankuang from '@/components/PedTankuang';
import { ElMessage, ElMessageBox } from 'element-plus';
import { setPhaseBarrier } from './barrier';
import PhaseDataModel from '@/components/PhaseDataModel';
import { useGlobalStore } from '@/store/modules/globalParam';
import { getPhase, pedimages } from '@/views/business/signalManagement/components/phase/utils';
import { getTheme } from '@/utils/theme';
import { getGlobalParamsMgr } from '@/components/EdgeMgr/globalManager';

export default defineComponent({
  name: 'phase',
  components: {
    XRDDirSelector,
    Tankuang,
    PedTankuang
  },
  setup(){
    const globalStore = useGlobalStore();
    const { tscParam, isRefreshTankuang } = storeToRefs(globalStore)
    return {
      globalStore,
      tscParam,
      isRefreshTankuang
    }
  },
  data() {
    return {
      phaseContainer: null as HTMLElement | null,
      singleTable: null as any,
      singleTables: null as any,
      formRef: null as any,
      phaseDataModel: null as PhaseDataModel | null,
      globalParamModel: null as any,
      tableHeight: 760,
      isShow: false,
      isShowPed: false,
      isShowPedTankuang: false,
      listLoading: false,
      dirArr: [] as any[],
      modalTitle: '阶段转环配置',
      boxVisible: false,
      tableList: [] as any[],
      form: { phase: [] as any[] },
      Data: { left: '60px', top: '10px' },
      Datas: { left: '60px', top: '10px' },
      Options: [] as any[],
      phaseOptions: [] as any[],
      sidewalkPhaseData: [] as any[],
      ConcurrentList: [] as any[],
      selectedOptions: [] as any[],
      range: 9,
      id: 1,
      showBottomName: false,
      lines: 4,
      rows: 4,
      controlTypeMap: {} as Record<number, string>,
      barriersList: [] as any[],
      controlTypeList: [
        { value: 0, label: '机动车主路' },
        { value: 1, label: '机动车支路' },
        { value: 2, label: '行人' },
        { value: 3, label: '公交专用' },
        { value: 4, label: 'BRT专用' },
        { value: 5, label: '有轨电车专用' },
        { value: 6, label: '非机动车专用' },
        { value: 99, label: '虚相位' }
      ] as any[],
      pulseTypeList: [
        { value: 0, label: '发送行人及机动车脉冲' },
        { value: 1, label: '发送机动车脉冲' },
        { value: 2, label: '发送行人脉冲' },
        { value: 3, label: '关闭行人及机动车脉冲' }
      ] as any[],
      refreshTankuang: false,
      StageChangeRingConcurrentList: [] as any[]
    };
  },

  directives: {
    clickoutside: {
      mounted(el: HTMLElement, binding: any) {
        const handler = (e: Event) => {
          if (!el.contains(e.target as Node)) {
            // 检查 binding.value 是否为函数
            if (typeof binding.value === 'function') {
              binding.value(e);
            }
          }
        };
        (el as any)._clickOutsideHandler = handler;
        document.addEventListener('click', handler);
      },
      unmounted(el: HTMLElement) {
        document.removeEventListener('click', (el as any)._clickOutsideHandler);
        delete (el as any)._clickOutsideHandler;
      }
    },
    clickoutsides: {
      mounted(el: HTMLElement, binding: any) {
        const handler = (e: Event) => {
          if (!el.contains(e.target as Node)) {
            // 检查 binding.value 是否为函数
            if (typeof binding.value === 'function') {
              binding.value(e);
            }
          }
        };
        (el as any)._clickOutsideHandler = handler;
        document.addEventListener('click', handler);
      },
      unmounted(el: HTMLElement) {
        document.removeEventListener('click', (el as any)._clickOutsideHandler);
        delete (el as any)._clickOutsideHandler;
      }
    },
    elSelectLoadmore: {
      mounted(el: any, binding: any) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        if (SELECTWRAP_DOM) {
          const handler = () => {
            const condition = SELECTWRAP_DOM.scrollHeight - SELECTWRAP_DOM.scrollTop - 1 <= SELECTWRAP_DOM.clientHeight;
            if (condition) binding.value();
          };
          SELECTWRAP_DOM.addEventListener('scroll', handler);
          el._scrollHandler = handler;
        }
      },
      unmounted(el: any) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        if (SELECTWRAP_DOM && el._scrollHandler) {
          SELECTWRAP_DOM.removeEventListener('scroll', el._scrollHandler);
          delete el._scrollHandler;
        }
      }
    }
  },

  computed: {
    formattedRings() {
      const ringMap: any = {};
      this.barriersList.forEach((barrier: any) => {
        barrier.items.forEach((item: any) => {
          const ringNumber = item.ring;
          const ringData = item.data.map((d: any) => 'P' + d).join(' ');
          if (!ringMap[ringNumber]) {
            ringMap[ringNumber] = {
              ringLabel: `Ring${ringNumber}`,
              values: []
            };
          }
          ringMap[ringNumber].values.push(ringData);
        });
      });
      return Object.values(ringMap).map((ring: any) => {
        return {
          ringLabel: ring.ringLabel,
          values: ring.values.join(' | ')
        };
      });
    },
    imgs () {
      const arrays: any[] = []
      const images = getPhase()
      images.forEach(v => {
        const obj = Object.assign({}, v)
        arrays.push(obj)
      })
      return arrays
    },
    pedimgs () {
      const arrays: any[] = []
      pedimages.forEach(v => {
        const obj = Object.assign({}, v)
        arrays.push(obj)
      })
      return arrays
    },
    list () {
      // 从全局参数管理器获取相位列表
      return this.globalParamModel ? this.globalParamModel.getParamsByType('phaseList') : []
    }
  },
  watch: {
    list: {
      handler: function (val) {
        this.initData()
      },
      deep: true
    },
    isRefreshTankuang: {
      handler: function (val) {
        if (val === 'phase') {
          this.refreshTankuang = true
        }
      }
    }
  },
  created () {
    this.phaseDataModel = new PhaseDataModel()
    // 使用全局参数管理器
    this.globalParamModel = getGlobalParamsMgr()
    this.increaseId()
    this.initData()
    this.controlTypeList.forEach(ele => {
      this.controlTypeMap[ele.value] = ele.label
    })
    this.uploadBarriers()
  },
  mounted() {
    // nextTick(() => {
    //   const container = this.$refs['phase-container'] as HTMLElement;
    //   if (container) {
    //     this.tableHeight = container.offsetHeight - 90;
    //   }
    //   window.onresize = () => {
    //     if (container) {
    //       this.tableHeight = container.offsetHeight - 90;
    //     }
    //   }
    // })
  },
  methods: {
    // 相位描述图像数据
    getPhaseImages() {
      return [
        { id: 1, zname: '东直行', ename: 'East-Straight', class: 'iconfont icon-dongzhihang' },
        { id: 2, zname: '东左转', ename: 'East-Left', class: 'iconfont icon-dongzuozhuan' },
        { id: 3, zname: '东右转', ename: 'East-Right', class: 'iconfont icon-dongyouzhuan' },
        { id: 4, zname: '东掉头', ename: 'East-Back', class: 'iconfont icon-dongdiaotou', leftclass: 'iconfont icon-dongdiaotouzuohang' },
        { id: 5, zname: '西直行', ename: 'West-Straight', class: 'iconfont icon-xizhihang' },
        { id: 6, zname: '西左转', ename: 'West-Left', class: 'iconfont icon-xizuozhuan' },
        { id: 7, zname: '西右转', ename: 'West-Right', class: 'iconfont icon-xiyouzhuan' },
        { id: 8, zname: '西掉头', ename: 'West-Back', class: 'iconfont icon-xidiaotou', leftclass: 'iconfont icon-xidiaotouzuohang' },
        { id: 9, zname: '北直行', ename: 'North-Straight', class: 'iconfont icon-beizhihang' },
        { id: 10, zname: '北左转', ename: 'North-Left', class: 'iconfont icon-beizuozhuan' },
        { id: 11, zname: '北右转', ename: 'North-Right', class: 'iconfont icon-beiyouzhuan' },
        { id: 12, zname: '北掉头', ename: 'North-Back', class: 'iconfont icon-beidiaotou', leftclass: 'iconfont icon-beidiaotouzuohang' },
        { id: 13, zname: '南直行', ename: 'South-Straight', class: 'iconfont icon-nanzhihang' },
        { id: 14, zname: '南左转', ename: 'South-Left', class: 'iconfont icon-nanzuozhuan' },
        { id: 15, zname: '南右转', ename: 'South-Right', class: 'iconfont icon-nanyouzhuan' },
        { id: 16, zname: '南掉头', ename: 'South-Back', class: 'iconfont icon-nandiaotou', leftclass: 'iconfont icon-nandiaotouzuohang' }
      ];
    },
    // 根据相位描述ID获取中文描述
    getPhaseDesc(list) {
      const images = this.getPhaseImages();
      let str = '';
      for (const ll of list) {
        for (const image of images) {
          if (image.id === ll) {
            str = str + ',' + image.zname;
          }
        }
      }
      if (str !== '') {
        str = str.slice(1);
      }
      return str;
    },
    getLable (str) {
      const match = str.match(/\d+/)
      if (match) {
        return this.getPhaseDescs(Number(match[0]))
      }
    },
    getPhaseDescs (id) {
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      const phasedesc = phaseList.filter((item) => {
        return item.id === id
      })[0].direction
      if (phasedesc.length === 0) return id
      const description = this.getPhaseDesc(phasedesc)
      return id + '-' + description
    },
    handlePhaseChange (val) {
      this.tableList = this.list.filter(item => val.includes(item.id))
    },
    barrier () {
      const rings = [[], [], [], []]
      // const phaseList = this.globalParamModel.getParamsByType('phaseList')
      for (const phase of this.tableList) {
        const ring: any = {
          name: '相位' + phase.id,
          id: phase.id
        }
        if (phase.newring === 1) {
          rings[0].push(ring)
        } else if (phase.newring === 2) {
          rings[1].push(ring)
        } else if (phase.newring === 3) {
          rings[2].push(ring)
        } else if (phase.newring === 4) {
          rings[3].push(ring)
        }
      }
      const barriers = setPhaseBarrier(rings, this.tableList)
      this.barriersList = barriers
    },
    jsonRing () {
      const setrings: any[] = []
      const ringMap: any = {}
      this.tableList.forEach(item => {
        const ring = item.newring
        const id = item.id
        if (!ringMap[ring]) {
          ringMap[ring] = []
        }
        ringMap[ring].push(id)
      })
      for (const [ring, ids] of Object.entries(ringMap)) {
        setrings.push({ num: Number(ring), sequence: ids })
      }
      return setrings
    },
    jsonPhase () {
      const phaseLists = this.tableList.map(ele => {
          return {
            id: ele.id,
            direction: ele.direction,
            ring: ele.newring,
            concurrent: ele.newconcurrent,
            peddirection: ele.peddirection
          }
        }
      )
      return phaseLists
    },
    uploadBarriers () {
      // const agentid = getIframdevid()
      // getPhasesBarrier(agentid).then(data => {
      //   if (!data.data.success) {
      //     if (data.data.code === '4002' && data.data.data.errorCode === '4209') {
      //       const success = data.data.data.content.success
      //       if (success !== 0) {
      //         const errormsg = 'openatccomponents.overview.putTscControlError' + success
      //         this.$message.error(this.$t(errormsg))
      //         return
      //       }
      //     }
      //     const parrenterror = getMessageByCode(data.data.code, this.$i18n.locale)
      //     if (data.data.data) {
      //       // 子类型错误
      //       const childErrorCode = data.data.data.errorCode
      //       if (childErrorCode) {
      //         const childerror = getMessageByCode(data.data.data.errorCode, this.$i18n.locale)
      //         this.$message.error(parrenterror + ',' + childerror)
      //       }
      //     } else {
      //       this.$message.error(parrenterror)
      //     }
      //     this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
      //     // return
      //   }
      //   if (data.data.data.stage2ringconfig) {
      //     this.form.phase = data.data.data.stage2ringconfig.realphases
      //     this.barriersList = data.data.data.stage2ringconfig.barriers
      //     this.tableList = data.data.data.stage2ringconfig.phaseList.map(ele => {
      //       return {
      //         id: ele.id,
      //         direction: ele.direction,
      //         newring: ele.ring,
      //         newconcurrent: ele.concurrent,
      //         peddirection: ele.peddirection
      //       }
      //     })
      //   }
      //   // this.$message.success(this.$t('edge.common.download'), { type: 'success' })
      // })
    },
    downBarriers () {
      // const obj = {
      //   agentid: getIframdevid(),
      //   stage2ringconfig: {
      //     realphases: this.form.phase,
      //     phaseList: this.jsonPhase(),
      //     rings: this.jsonRing(),
      //     barriers: this.barriersList
      //   }
      // }
      // downPhasesBarrier(obj).then(data => {
      //   this.boxVisible = false
      //   if (!data.data.success) {
      //     if (data.data.code === '4002' && data.data.data.errorCode === '4209') {
      //       const success = data.data.data.content.success
      //       if (success !== 0) {
      //         const errormsg = 'openatccomponents.overview.putTscControlError' + success
      //         this.$message.error(this.$t(errormsg))
      //         return
      //       }
      //     }
      //     const parrenterror = getMessageByCode(data.data.code, this.$i18n.locale)
      //     if (data.data.data) {
      //       // 子类型错误
      //       const childErrorCode = data.data.data.errorCode
      //       if (childErrorCode) {
      //         const childerror = getMessageByCode(data.data.data.errorCode, this.$i18n.locale)
      //         this.$message.error(parrenterror + ',' + childerror)
      //       }
      //     } else {
      //       this.$message.error(parrenterror)
      //     }
      //     this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
      //     return
      //   }
      //   this.$message.success(this.$t('edge.common.download'), { type: 'success' })
      // })
    },
    oncancle () {
      this.boxVisible = false
    },
    onok () {
      this.downBarriers()
    },
    stageChangeRing () {
      this.boxVisible = true
    },
    loadMore () {
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      if (this.range > phaseList.length) return
      this.range += 2
    },
    increaseId () { // 实现id在之前的基础上寻找最小的
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      // console.log(phaseList, 'phaseList')
      const phaseIdList = phaseList.map(ele => ele.id)
      const i = phaseList.length - 1
      if (i >= 0) {
        for (let j = 1; j <= 40; j++) {
          if (!phaseIdList.includes(j)) {
            this.id = j
            return
          }
        }
      }
    },
    handleEdit (index, row) {

    },
    handleRowClick (row) {
      const table = this.$refs.singleTable as any;
      if (table) {
        table.setCurrentRow(row);
      }
      if (Number(row.linkphase) > 9) {
        this.Options.push({
          value: Number(row.linkphase),
          label: '相位' + row.linkphase
        })
      }
    },
    showIcon (data) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      const dirArrs: any[] = []
      phaseList.filter((item) => {
        if (item.id === data) {
          if (item.direction.length === 0) {
            const recd = {
              id: 0,
              peddirection: this.getPed(item.peddirection),
              color: color
            }
            dirArrs.push(recd)
          } else {
            for (const rec of item.direction) {
              const recd = {
                id: rec,
                peddirection: this.getPed(item.peddirection),
                color: color
              }
              dirArrs.push(recd)
            }
          }
        }
      })
      return dirArrs
    },
    getPhaseById (id) {
      const phaselist = this.globalParamModel.getParamsByType('phaseList')
      if (id === undefined || id === '') return ''
      const phasedesc = phaselist.filter((item) => {
        return item.id === id
      })[0].direction
      if (phasedesc.length === 0) return id
      const description = this.getPhaseDesc(phasedesc)
      return id + '-' + description
    },
    getPed (data) {
      if (data.length === 0) return
      const ped: any[] = []
      let peddirections: any[] = []
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      for (const walk of this.sidewalkPhaseData) {
        for (const ped of data) {
          const objs: any = {
            name: walk.name,
            id: walk.id,
            color: color
          }
          if (ped === walk.id) {
            peddirections.push(objs)
            peddirections = Array.from(new Set(peddirections))
          }
        }
      }
      ped.push(...peddirections)
      return ped
    },
    getPedPhasePos (phaseList) {
      // 行人相位信息
      this.sidewalkPhaseData = []
      phaseList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
            // 行人相位
            if (this.phaseDataModel.getSidePos(dir)) {
              this.sidewalkPhaseData.push({
                // key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.phaseDataModel.getSidePos(dir).name
              })
            }
          })
        }
      })
      return this.sidewalkPhaseData
    },
    setOption (list) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      this.Options = list.map((item, index) => {
        const dirArr: any[] = []
        if (item.direction.length === 0) {
          const recd = {
            id: 0,
            peddirection: this.getPed(item.peddirection),
            color: color
          }
          this.dirArr.push(recd)
        } else {
          for (const rec of item.direction) {
            const recd = {
              id: rec,
              peddirection: this.getPed(item.peddirection),
              color: color
            }
            dirArr.push(recd)
          }
        }
        let name = ''
        name = '相位' + item.id
        return {
          label: name,
          item: dirArr,
          value: item.id
        }
      })
    },
    setPhaseOption (list) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      this.phaseOptions = list.map((item, index) => {
        const dirArr: any[] = []
        if (item.direction.length === 0) {
          const recd = {
            id: 0,
            peddirection: this.getPed(item.peddirection),
            color: color
          }
          this.dirArr.push(recd)
        } else {
          for (const rec of item.direction) {
            const recd = {
              id: rec,
              peddirection: this.getPed(item.peddirection),
              color: color
            }
            dirArr.push(recd)
          }
        }
        let name = ''
        name = '相位' + item.id
        return {
          label: name,
          item: dirArr,
          value: item.id
        }
      })
    },
    changeLink (val) {
      this.range = 9
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      this.setOption(phaseList)
      // this.linkPhaseOption = phaseList
    },
    initData () {
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      this.getPedPhasePos(phaseList)
      this.setPhaseOption(phaseList)
      // for (let i = 0; i < phaseList.length; i++) {
      //   if (phaseList[i].controltype !== 99) {
      //     let pattern = {}
      //     var patternNum = phaseList[i].id
      //     var patternDescription
      //     if (phaseList[i].direction.length > 0 && phaseList[i].direction !== undefined) {
      //       patternDescription = patternNum + '-' + getPhaseDesc(phaseList[i].direction, this.$i18n.locale)
      //     } else {
      //       patternDescription = patternNum
      //     }
      //     pattern.value = patternNum
      //     pattern.label = patternDescription
      //     this.linkPhaseOption.push(pattern)
      //   }
      // }
    },
    handleDelete (index, row) {
      ElMessageBox.confirm(
        '确认删除此相位？',
        '警告',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.globalParamModel.deleteParamsByType('phaseList', index, 1)
        this.deleteRing(row)
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        // 删除关联此行的并发相位
        this.handleDeleteConcurrent(row.id, [], row.concurrent, this.list)

      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleNumber () {
      ElMessageBox.prompt('请输入相位编号', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        // inputValue: this.phaseid,
        inputValidator: (value) => {
          if (value === undefined || value === null || value.replace(/\s/g, '') === '') {
            // 相位编号必填校验
            return '必须填写相位编号！'
          }
          // 相位编号不能重复校验
          const inputvalue = value.replace(/\s/g, '')
          const phaseList = this.globalParamModel.getParamsByType('phaseList')
          for (const obj of phaseList) {
            const curdesc = obj.id
            if (curdesc === Number(inputvalue)) {
              return '相位编号不能重复！'
            }
          }
          return true
        }
      }).then(({ value }) => {
        this.increaseId()
        const phaseInitData = {
          id: Number(value),
          direction: [],
          peddirection: [], // 行人方向
          mingreen: 15,
          max1: 60,
          max2: 90,
          passage: 3,
          phasewalk: 0,
          pedclear: 10,
          yellow: 3,
          redclear: 2,
          flashgreen: 6,
          redyellow: 0,
          ring: 1,
          greenpulse: 10,
          redpulse: 10,
          pedgreenpulse: 0,
          vehiclethresh: 30,
          pedestrianthresh: 30,
          linkphase: '',
          controltype: 0, // 控制类型
          concurrent: []
        }
        this.globalParamModel.addParamsByType('phaseList', phaseInitData)
        this.editRing(phaseInitData)
        // 重新排序相位数组
        const phaseList = this.globalParamModel.getParamsByType('phaseList')
        phaseList.sort(this.compareProperty('id'))
      }).catch(() => {
        // 用户取消输入
      })
    },
    handleClone (index, row) {
      const data: any = {}
      this.increaseId()
      if (this.globalParamModel.getParamLength('phaseList') >= 40) {
        ElMessage.error('最多只能创建40条数据！')
        return
      }
      Object.assign(data, row)
      data.id = this.id
      this.globalParamModel.addParamsByType('phaseList', data)
      // this.id++
      this.editRing(data)
      // 重新排序相位数组
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      phaseList.sort(this.compareProperty('id'))
    },
    deleteAllData () {
      ElMessageBox.confirm(
        '确认删除所有通道？',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        const phaseList = this.globalParamModel.getParamsByType('phaseList')
        const patternList = this.globalParamModel.getParamsByType('patternList')
        const overlaplList = this.globalParamModel.getParamsByType('overlaplList')
        this.globalParamModel.deleteParamsByType('overlaplList', 0, overlaplList.length)
        this.globalParamModel.deleteParamsByType('phaseList', 0, phaseList.length)
        this.globalParamModel.deleteParamsByType('patternList', 0, patternList.length)
        this.id = 1
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    onAdd () {
      this.increaseId()
      if (this.globalParamModel.getParamLength('phaseList') >= 40) {
        ElMessage.error('最多只能创建40条数据！')
        return
      }
      var phaseInitData = {
        id: this.id,
        direction: [],
        peddirection: [], // 行人方向
        mingreen: 15,
        max1: 60,
        max2: 90,
        passage: 3,
        phasewalk: 0,
        pedclear: 10,
        yellow: 3,
        redclear: 2,
        flashgreen: 6,
        redyellow: 0,
        ring: 1,
        greenpulse: 10,
        redpulse: 10,
        pedgreenpulse: 0,
        gapout: 0,
        vehiclethresh: 30,
        pedestrianthresh: 30,
        linkphase: '',
        controltype: 0, // 控制类型
        concurrent: []
      }
      this.globalParamModel.addParamsByType('phaseList', phaseInitData)
      // this.id++
      this.editRing(phaseInitData)
      // 重新排序相位数组
      let phaseList = this.globalParamModel.getParamsByType('phaseList')
      phaseList.sort(this.compareProperty('id'))
      console.log('phaseList',phaseList)
    },
    compareProperty (property) {
      return function (a, b) {
        const value1 = a[property]
        const value2 = b[property]
        return value1 - value2
      }
    },
    getPhaseListConcurrent (value, status) {
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      this.ConcurrentList = this.getConcurrent(value, status, phaseList)
    },
    getStageChangeRingConcurrent (value, status) {
      this.StageChangeRingConcurrentList = this.getConcurrent(value, status, this.tableList, 'newring')
    },
    getConcurrent (value, status, phaseList, ringfield = 'ring') {
      const ConcurrentList: any[] = []
      if (status) {
        for (let i = 0; i < phaseList.length; i++) {
          if (phaseList[i][ringfield] !== value[ringfield]) {
            const patternNum = phaseList[i].id
            let patternDescription
            if (phaseList[i].direction.length > 0 && phaseList[i].direction !== undefined) {
              patternDescription = patternNum + '-' + this.getPhaseDesc(phaseList[i].direction)
            } else {
              patternDescription = patternNum
            }
            const pattern: any = {
              value: patternNum,
              label: patternDescription
            }
            // this.linkPhaseOption.push(pattern)
            ConcurrentList.push(pattern)
          }
        }
      }
      return ConcurrentList
    },
    getConcurrentstrs (val) {
      if (val.newconcurrent) {
        const concurrent = val.newconcurrent
        if (concurrent.length === 0) {
          return ''
        } else {
          let str = ''
          for (const con of concurrent) {
            str = str + ',' + con
          }
          return str.substr(1)
        }
      }
      return ''
    },
    getConcurrentstr (val) {
      if (val.concurrent) {
        const concurrent = val.concurrent
        if (concurrent.length === 0) {
          return ''
        } else {
          let str = ''
          for (const con of concurrent) {
            str = str + ',' + con
          }
          return str.substr(1)
        }
      }
      return ''
    },
    getPulsetypestr (val) {
      if (val !== undefined) {
        const choosed = this.pulseTypeList.filter(ele => ele.value === val)
        if (choosed.length) {
          return choosed[0].label
        }
      }
      return ''
    },
    getControlTypestr (val) {
      return this.controlTypeMap[val] || ''
      // if (val.controltype !== undefined) {
      //   return this.controlTypeList.filter(ele => ele.value === val.controltype)[0].label
      // }
      // return ''
    },
    getDescritionstr (val) {
      const desc = val.direction
      if (desc.length === 0) {
        return ''
      } else {
        let str = ''
        for (const des of desc) {
          str = str + ',' + des
        }
        return str.substr(1)
      }
    },
    editRing (value) {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let id = value.id
      for (let pattern of patternList) {
        let ring: any = {
          name: 'Phase ' + id,
          id: id,
          value: 30
        }
        pattern.rings[value.ring - 1].push(ring)
        pattern.cycle = pattern.cycle + 30
      }
    },
    handleRingEdit (index, row) {
      const patternList = this.globalParamModel.getParamsByType('patternList')
      const id = row.id
      for (const pattern of patternList) {
        const newRing: any = {}
        for (const ring of pattern.rings) {
          for (let i = 0; i < ring.length; i++) {
            if (ring[i].id === id) {
              newRing.name = ring[i].name
              newRing.value = ring[i].value
              newRing.id = id
              ring.splice(i, 1)
            }
          }
        }
        const i = row.ring - 1
        pattern.rings[i].push(newRing)
      }
    },
    deleteRing (row) {
      const patternList = this.globalParamModel.getParamsByType('patternList')
      const id = row.id
      const ring = row.ring - 1
      for (const pattern of patternList) {
        for (let i = 0; i < pattern.rings[ring].length; i++) {
          if (pattern.rings[ring][i].id === id) {
            pattern.rings[ring].splice(i, 1)
            pattern.cycle = pattern.cycle - 30
          }
        }
      }
    },
    cancelTable (e) {
      const table = this.$refs.singleTable as any;
      if (table) {
        table.setCurrentRow();
      }
    },
    cancelTables (e) {
      const table = this.$refs.singleTables as any;
      if (table) {
        table.setCurrentRow();
      }
    },
    checkLane (value, index) {
    },
    handlefinsh (value) {
      const index = value.index
      const status = value.status
      const list: any[] = []
      for (let i = 0; i < status.length; i++) {
        if (!status[i]) continue
        list.push(this.imgs[i].id)
      }
      this.globalParamModel.getParamsByType('phaseList')[index].direction = list

      // 触发刷新以同步所有组件状态
      this.refreshTankuang = !this.refreshTankuang
    },
    handlefinshped (value) {
      const index = value.index
      const status = value.status
      const list = []
      for (let i = 0; i < status.length; i++) {
        if (!status[i]) continue
        list.push(this.pedimgs[i].id)
      }
      this.globalParamModel.getParamsByType('phaseList')[index].peddirection = list

      // 触发刷新以同步所有组件状态
      this.refreshTankuang = !this.refreshTankuang
    },
    handleChangeConcurrent (row, value, phaseList, concurrentField = 'concurrent') {
      if (!phaseList || !phaseList.length) return
      for (let i = 0; i < phaseList.length; i++) {
        if (phaseList[i].id === row.id) {
          phaseList[i][concurrentField] = value
        }
      }
    },
    handleDeleteConcurrent (curid, curVal, oldVal, list, concurrentField = 'concurrent') {
      // 删除关联的并发相位
      for (let i = 0; i < oldVal.length; i++) {
        if (curVal.indexOf(oldVal[i]) === -1) {
          // 此项已被删除
          for (let j = 0; j < list.length; j++) {
            if (list[j].id === oldVal[i]) {
              const index = list[j][concurrentField].indexOf(curid)
              if (index !== -1) {
                list[j][concurrentField].splice(index, 1)
              }
            }
          }
        }
      }
    },
    handleAddConcurrent (curid, curVal, list, concurrentField = 'concurrent') {
      // 处理现有关联相位
      curVal.forEach(concurrent => {
        for (let i = 0; i < list.length; i++) {
          const listconcurrent = list[i][concurrentField]
          const listid = list[i].id
          if (listid === concurrent) {
            if (listconcurrent.indexOf(curid) === -1) {
              listconcurrent.push(curid)
            }
          }
          // else if (listid !== curid) {
          //   if (listconcurrent.indexOf(curid) && curVal.indexOf(listid) === -1) {
          //   }
          // }
        }
      })
    },
    associateConcurrent (row, value) {
      const curid = row.id
      const concurrentlist = row.concurrent
      this.handleDeleteConcurrent(curid, value, concurrentlist, this.list)
      this.handleAddConcurrent(curid, value, this.list)
      // 关联后，再修改对应值
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      this.handleChangeConcurrent(row, value, phaseList)
    },
    associateStageChangeRingConcurrent (row, value) {
      const curid = row.id
      const concurrentlist = row.newconcurrent
      this.handleDeleteConcurrent(curid, value, concurrentlist, this.tableList, 'newconcurrent')
      this.handleAddConcurrent(curid, value, this.tableList, 'newconcurrent')
      // 关联后，再修改对应值
      this.handleChangeConcurrent(row, value, this.tableList, 'newconcurrent')
    }
  }
});
</script>
<style>
body {
  overflow: auto;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
}

/* 表格编辑样式 - 默认隐藏输入控件，显示文本 */
.tb-edit .el-input {
  display: none;
}
.tb-edit .current-row .el-input {
  display: block;
}
.tb-edit .current-row .el-input + span {
  display: none;
}

/* 并发相位多选框始终显示 */
.tb-edit .el-select {
  display: none;
}
.tb-edit .el-table-column:nth-child(6) .el-select {
  display: block; /* 并发相位列的多选框始终显示 */
}
.tb-edit .current-row .el-select {
  display: block;
}
.tb-edit .current-row .el-select + span {
  display: none;
}
/* 并发相位列的span在非编辑状态下隐藏 */
.tb-edit .el-table-column:nth-child(6) span {
  display: none;
}

.tb-edit .el-input-number {
  display: none;
}
.tb-edit .current-row .el-input-number {
  display: block;
}
.tb-edit .current-row .el-input-number + span {
  display: none;
}

.tb-edit .el-col {
  display: none;
}
.tb-edit .current-row .el-col {
  display: block;
}
.tb-edit .current-row .el-col + span {
  display: none;
}

.tb-edit .el-popover {
  display: none;
}
.tb-edit .current-row .el-popover {
  display: block;
}

/* 显示文本的样式控制 */
.showSpan {
  display: block;
}
.tb-edit .current-row .showSpan {
  display: none;
}

.showDirIcon {
  display: block;
}
.tb-edit .current-row .showDirIcon {
  display: none;
}

/* 修复PedestrianSelector在表格编辑模式下的样式问题 */
.tb-edit .pedestrian-selector .selector-input {
  vertical-align: middle;
}

.tb-edit .current-row .pedestrian-selector .selector-input {
  vertical-align: middle;
}

/* 确保非当前行显示选中结果 */
.tb-edit .pedestrian-selector .dir-icon-container {
  display: inline-block;
}

.tb-edit .current-row .pedestrian-selector .dir-icon-container {
  display: none;
}

/* 修复DirectionSelector在表格编辑模式下的样式问题 */
.tb-edit .direction-selector .selector-input {
  vertical-align: middle;
}

.tb-edit .current-row .direction-selector .selector-input {
  vertical-align: middle;
}

/* 确保非当前行显示选中结果 */
.tb-edit .direction-selector .dir-icon-container {
  display: inline-block;
}

.tb-edit .current-row .direction-selector .dir-icon-container {
  display: none;
}

/* 相位容器样式 */
.phase-container {
  height: 100%;
  /* padding: 20px; */
  /* background-color: #fff; */
  /* border-radius: 8px; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

/* 按钮组样式 */
.phase-container .el-button {
  margin-right: 10px;
}

/* 表格样式优化 */
.tb-edit {
  margin-top: 20px;
}

.tb-edit .el-table__row {
  cursor: pointer;
}

.tb-edit .el-table__row:hover {
  background-color: #f5f7fa;
}

.tb-edit .current-row {
  background-color: #ecf5ff !important;
}

/* 单选模型样式 */
.single-model {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.single-model span {
  margin-left: 10px;
  color: #8492a6;
  font-size: 13px;
}

/* 对话框样式 */
.boxFooter {
  text-align: right;
  padding-top: 20px;
}

.boxFooter .btn {
  margin-left: 10px;
}

.canclebtn {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}

.okbtn {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

/* 表格列样式 */
.table-column {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .phase-container {
    padding: 15px;
  }

  .tb-edit .el-table {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .phase-container {
    padding: 10px;
  }

  .phase-container .el-button {
    margin-bottom: 10px;
    width: 100%;
  }
}
</style>

<style scoped>
/* Element Plus 组件样式覆盖 */
:deep(.el-select .el-tag__close.el-icon-close) {
  color: #202940;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}

:deep(.el-select .el-input) {
  cursor: pointer;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 相位选择器样式 */
:deep(.el-option) {
  padding: 0;
}

:deep(.el-option .single-model) {
  padding: 8px 20px;
}

/* 表格行编辑状态样式 */
:deep(.tb-edit .current-row .el-input) {
  width: 100%;
}

:deep(.tb-edit .current-row .el-select) {
  width: 100%;
}

:deep(.tb-edit .current-row .el-input-number) {
  width: 100%;
}

/* 加载状态样式 */
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 按钮样式优化 */
:deep(.el-button--link) {
  padding: 0;
  font-size: 12px;
}

/* 颜色选择器样式 */
:deep(.el-color-picker) {
  vertical-align: middle;
}

/* 开关样式 */
:deep(.el-switch) {
  vertical-align: middle;
}

/* 输入框数字样式 */
:deep(.el-input-number .el-input__inner) {
  padding-left: 8px;
  padding-right: 8px;
}

/* 选择框样式 */
:deep(.el-select .el-input .el-select__caret) {
  color: #c0c4cc;
}

:deep(.el-select .el-input.is-focus .el-select__caret) {
  color: #409eff;
}

/* 表格固定列样式 */
:deep(.el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 分割线样式 */
:deep(.el-divider) {
  margin: 20px 0;
}

/* 屏障显示样式 */
.barrier-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.barrier-display strong {
  color: #495057;
  font-weight: 600;
}
</style>
<style scoped>
:deep(.tb-edit .el-select .el-tag__close.el-icon-close) {
  color: #202940;
}
</style>
