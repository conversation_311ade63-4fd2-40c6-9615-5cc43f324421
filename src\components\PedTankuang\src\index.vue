<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElPopover, ElButton, ElInput } from 'element-plus';
import XRDDirSelector from '@/components/XRDDirSelector';
import { getTheme } from '@/utils/theme';
import type {
  PedestrianSelectorProps,
  PedestrianSelectorEmits,
  PedestrianDirection
} from './types';
import PhaseDataModel from '@/components/PhaseDataModel';
const props = withDefaults(defineProps<PedestrianSelectorProps>(), {
  imgs: () => [],
  list: () => [],
  index: 0,
  disabled: false,
  showBottomName: false,
  lines: 4,
  rows: 3,
  showSpan: true,
  refresh: false,
  showDirIcon: false,
  laneIconStyle: () => ({ left: '60px', top: '10px' }),
  pedIconStyle: () => ({ left: '60px', top: '15px' })
});
const emit = defineEmits<PedestrianSelectorEmits>();
const popoverRef = ref<InstanceType<typeof ElPopover>>();
const visible = ref(false);
const dialogVisible = ref(true);
const name = ref('');
const isRoutine = ref(false);
const status = ref<number[]>([]);
const sum = ref(0);
// 安全的图片列表
const imgs = computed(() => props.imgs || []);
// 刷新数据
const refreshData = () => {
  status.value = imgs.value.map(item =>
    props.list.includes(item.id) ? 1 : 0
  );
  name.value = getName();
  sum.value = props.lines * props.rows;
};
// 获取显示名称
const getName = (): string => {
  return imgs.value
    .filter((_, i) => status.value[i])
    .map(item => item.name)
    .join(',');
};
// 切换行人方向类型
const changeLane = () => {
  isRoutine.value = true;
  updatePopover();
};
const changeLanes = () => {
  isRoutine.value = false;
  updatePopover();
};
// 更新弹窗位置
const updatePopover = () => {
  if (!popoverRef.value) return;

  nextTick(() => {
    // 使用 any 类型断言来访问内部方法
    (popoverRef.value as any).updatePopper?.();
  });
};
// 处理选择框点击
const boxShow = (index: number) => {
  status.value[index] = status.value[index] ? 0 : 1;
  name.value = getName();
  toParent();
};
// 向父组件传递数据
const toParent = () => {
  emit('finsh', {
    status: status.value,
    index: props.index
  });
};
const hidePopver  = () => {
  dialogVisible.value = false
  setTimeout(() => {
    dialogVisible.value = true
  }, 10)
}
// 创建单例 PhaseDataModel 实例
const phaseDataModel = new PhaseDataModel();

// 获取行人方向列表
const getPedDirectionList = (peddirection: number[]): PedestrianDirection[] => {
  const dirArr = []
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
  const recd = {
    peddirection: getPed(peddirection),
    color: color
  }
  dirArr.push(recd)
  return dirArr
};
// 获取行人方向
const getPed = (data: number[]): Array<{ id: number; name: string; color: string }> => {
  if (data.length === 0) return [];
  const ped: Array<{ id: number; name: string; color: string }> = [];
  let peddirections: Array<{ id: number; name: string; color: string }> = [];
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'

  for (const walk of data) {
    const sidePos = phaseDataModel.getSidePos(walk);
    const objs = {
      name: sidePos?.name || `行人方向${walk}`,
      id: walk,
      color: color
    }
    peddirections.push(objs)
    peddirections = Array.from(new Set(peddirections))
  }
  ped.push(...peddirections);
  return ped;
};
// 计算弹框大小
const tankuangSize = computed(() => {
  const lines = props.lines;
  const widths = ((100 / lines) - 2) + '%';
  console.log('tankuangSize=',widths)
  return {
    width: widths
  };
});
// 初始化数据
refreshData();
// 监听刷新标志和列表变化
watch(() => props.refresh, refreshData);
watch(() => props.list, refreshData, { deep: true });
watch(() => props.imgs, refreshData, { deep: true });
</script>
<template>
  <div class="pedestrian-selector">
    <el-popover
      ref="popoverRef"
      placement="bottom"
      :width="280"
      trigger="click"
      v-model:visible="visible"
      @before-leave="hidePopver"
      v-if="!disabled && dialogVisible"
    >
      <div class="popover-header">
        <span class="header-label">车道</span>
        <el-button
          v-if="!isRoutine"
          @click="changeLane"
          link
          class="toggle-button"
        >
          拓展行人方向 >>
        </el-button>
        <el-button
          v-else
          @click="changeLanes"
          link
          class="toggle-button"
        >
          常规行人方向 >>
        </el-button>
      </div>
      <!-- 常规模式 -->
      <div v-if="!isRoutine">
        <!-- 行人过街 -->
        <div class="label">行人过街</div>
        <div class="main oneRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 0 && index <= 3" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '100%', 'height': '56px', 'object-fit': 'contain'}"/>
            </div>
          </template>
        </div>

        <!-- 行人二次过街 -->
        <div class="label">行人二次过街</div>
        <div class="main twoRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 4 && index <= 11" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '80%', 'height': '45px', 'object-fit': 'contain'}"/>
            </div>
          </template>
        </div>

        <!-- 斜向行人过街 -->
        <div class="label">斜向行人过街</div>
        <div class="main oneRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 12 && index <= 13" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '80%', 'height': '45px', 'object-fit': 'contain'}"/>
            </div>
          </template>
        </div>

        <!-- 路段行人过街 -->
        <div class="label">路段行人过街</div>
        <div class="main oneRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 14 && index <= 15" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '80%', 'height': '45px', 'object-fit': 'contain'}"/>
            </div>
          </template>
        </div>
      </div>

      <!-- 拓展模式 -->
      <div v-else>
        <!-- 对角斑马线 -->
        <div class="label">对角斑马线</div>
        <div class="main oneRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 16 && index <= 19" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '80%', 'height': '45px', 'object-fit': 'contain'}" />
            </div>
          </template>
        </div>

        <!-- 拓展行人过街 -->
        <div class="label">拓展行人过街</div>
        <div class="main twoRowH">
          <template v-for="(item, index) in imgs" :key="index">
            <div v-if="index >= 20 && index <= 27" :style="tankuangSize" :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'" @click="boxShow(index)">
              <div v-if="item.class" :class="item.class" style="border:0px"></div>
              <img v-else-if="item.img" :src="item.img" :style="{'border':'0px', 'width': '80%', 'height': '45px', 'object-fit': 'contain'}" />
            </div>
          </template>
        </div>
      </div>
      <span  v-show="showBottomName" class="show-span">
        {{ name }}
      </span>
      <template #reference>
        <el-input
          :disabled="disabled"
          size="small"
          v-model="name"
          readonly
          class="selector-input"
        />
      </template>
    </el-popover>

    <!-- 统一的图标显示逻辑 -->
    <template v-if="(disabled || showDirIcon) && list.length > 0">
      <span class="dir-icon-container">
        <XRDDirSelector
          :Data="laneIconStyle"
          :Datas="pedIconStyle"
          Width="60px"
          Height="60px"
          :showlist="getPedDirectionList(list)"
        />
      </span>
    </template>

    <span v-show="showSpan" class="show-span">
      {{ name }}
    </span>

  </div>
</template>
<style scoped lang="scss">
.pedestrian-selector {
  display: inline-block;
  vertical-align: middle;

  .popover-header {
    display: flex;
    margin-bottom: 12px;

    .header-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .toggle-button {
      padding: 0;
      font-size: 12px;
      margin-left: auto;
    }
  }

  .main {
    width: 100%;
    padding-left: 0.5%;
    overflow: hidden;
    margin-bottom: 10px;
  }

  .oneRowH {
    height: 71px;
  }

  .twoRowH {
    height: 128px;
  }

  .label {
    font-size: 12px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 5px;
    margin-top: 10px;
    clear: both;
  }

  .phaseNoSelected {
    border: 1px solid #dcdfe6;
    background-color: #ecf5ff;
    border-radius: 4px;
    float: left;
    margin: 0.25%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }

  .phaseSelected {
    border: 1px solid #409eff;
    background-color: #409eff;
    border-radius: 4px;
    float: left;
    margin: 0.25%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
  }

  .show-span {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: #606266;
  }

  .hidden-span {
    display: none;
  }

  .selector-input {
    width: 200px;
    vertical-align: middle;
  }

  // 修复在表格编辑模式下的输入框定位问题
  .selector-input :deep(.el-input__inner) {
    vertical-align: middle;
  }

  .dir-icon-container {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
  }
}

/* 修复el-popover内部布局问题 */
:deep(.el-popover) {
  .popover-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    min-width: 260px !important;

    .header-label {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #333 !important;
      flex-shrink: 0 !important;
    }

    .toggle-button {
      padding: 0 !important;
      font-size: 12px !important;
      margin-left: auto !important;
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }
}
</style>

<style lang="scss">
/* 全局样式修复popover布局 - 不使用scoped，确保能够覆盖popover内部样式 */
.el-popover {
  .popover-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12px !important;
    min-width: 260px !important;

    .header-label {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #333 !important;
      flex-shrink: 0 !important;
    }

    .toggle-button {
      padding: 0 !important;
      font-size: 12px !important;
      margin-left: auto !important;
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }
}

/* 修复表格编辑模式下PedestrianSelector的样式问题 */
.tb-edit .pedestrian-selector {
  .selector-input {
    vertical-align: middle !important;
  }

  .selector-input .el-input__inner {
    vertical-align: middle !important;
  }
}
</style>
