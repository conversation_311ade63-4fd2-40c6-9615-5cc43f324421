<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="所属路口" prop="algorithmIntersectionId">
              <el-select v-model="queryParams.algorithmIntersectionId" placeholder="请选择路口">
                <el-option v-for="dict in intersectionList" :key="dict.id" :label="dict.intersectionName"
                  :value="dict.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="算法名称" prop="algorithmName">
              <el-input v-model="queryParams.algorithmName" placeholder="请输入算法名称" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['business:intersectionAlgorithm:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['business:intersectionAlgorithm:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['business:intersectionAlgorithm:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['business:intersectionAlgorithm:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="intersectionAlgorithmList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="所属路口" align="center" prop="intersectionName" />
        <el-table-column label="算法名称" align="center" prop="algorithmName" />
        <!-- <el-table-column label="算法描述" align="center" prop="algorithmDesc" /> -->
        <!-- <el-table-column label="算法配置" align="center" prop="algorithmConfig" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['business:intersectionAlgorithm:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['business:intersectionAlgorithm:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改路口算法对话框 -->
    <el-drawer :title="drawer.title" v-model="drawer.visible" size="50%" append-to-body>
      <el-form ref="intersectionAlgorithmFormRef" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" placeholder="请输入主键" />
        </el-form-item> -->
        <el-form-item label="所属路口" prop="algorithmIntersectionId">
          <el-select v-model="form.algorithmIntersectionId" placeholder="请选择路口">
            <el-option v-for="dict in intersectionList" :key="dict.id" :label="dict.intersectionName"
              :value="dict.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="算法名称" prop="algorithmName">
          <el-input v-model="form.algorithmName" placeholder="请输入算法名称" />
        </el-form-item>
        <el-form-item label="算法配置" prop="algorithmConfig">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.algorithmConfig" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
        <el-form-item label="算法描述" prop="algorithmDesc">
          <el-input v-model="form.algorithmDesc" type="textarea" :rows="5" placeholder="请输入算法描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="IntersectionAlgorithm" lang="ts">
import { debounce } from 'lodash'

import { listIntersectionAlgorithm, getIntersectionAlgorithm, delIntersectionAlgorithm, addIntersectionAlgorithm, updateIntersectionAlgorithm } from '@/api/business/intersectionAlgorithm';
import { listIntersectionDrop } from '@/api/business/intersectionInfo';
import { IntersectionAlgorithmVO, IntersectionAlgorithmQuery, IntersectionAlgorithmForm } from '@/api/business/intersectionAlgorithm/types';
import JsonEditorVue from 'json-editor-vue3'

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const intersectionAlgorithmList = ref<IntersectionAlgorithmVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const intersectionAlgorithmFormRef = ref<ElFormInstance>();

const drawer = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: IntersectionAlgorithmForm = {
  id: undefined,
  algorithmName: undefined,
  algorithmDesc: undefined,
  algorithmConfig: undefined,
  algorithmIntersectionId: undefined,
}
const data = reactive<PageData<IntersectionAlgorithmForm, IntersectionAlgorithmQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    algorithmName: undefined,
    algorithmIntersectionId: undefined,
    params: {
    }
  },
  rules: {
    algorithmName: [
      { required: true, message: "算法名称不能为空", trigger: "blur" }
    ],
    algorithmConfig: [
      { required: true, message: "算法配置不能为空", trigger: "blur" }
    ],
    algorithmIntersectionId: [
      { required: true, message: "所属路口不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const intersectionList = ref([]);
const jsonValid = ref<boolean>(true);

/** 查询路口算法列表 */
const getList = async () => {
  loading.value = true;
  const res = await listIntersectionAlgorithm(queryParams.value);
  intersectionAlgorithmList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  drawer.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  jsonValid.value = true;
  intersectionAlgorithmFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: IntersectionAlgorithmVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  drawer.visible = true;
  drawer.title = "添加路口算法";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: IntersectionAlgorithmVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getIntersectionAlgorithm(_id);
  Object.assign(form.value, res.data);


  if (res.data.algorithmConfig) {
    const algorithmConfigStr = res.data.algorithmConfig.toString();
    form.value.algorithmConfig = JSON.parse(algorithmConfigStr);
    jsonValid.value = true;
  }

  drawer.visible = true;
  drawer.title = "修改路口算法";
}

/** 提交按钮 */
const submitForm = () => {
  intersectionAlgorithmFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await nextTick(); // 确保状态更新完成
      if (!jsonValid.value) {
        proxy?.$modal.msgError("算法配置 JSON 格式错误");
        return;
      }

      buttonLoading.value = true;
      if (form.value.id) {
        await updateIntersectionAlgorithm(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addIntersectionAlgorithm(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      drawer.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: IntersectionAlgorithmVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除路口算法编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delIntersectionAlgorithm(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('business/intersectionAlgorithm/export', {
    ...queryParams.value
  }, `intersectionAlgorithm_${new Date().getTime()}.xlsx`)
}

const setupIntersectionDrop = async () => {
  const res = await listIntersectionDrop();
  intersectionList.value = res.data;
}

const handleValidationError = (errors) => {
  jsonValid.value = false;
}

const handleJsonUpdate = debounce((newJson) => {
  jsonValid.value = true;
}, 300)

onMounted(() => {
  getList();
  setupIntersectionDrop();
});
</script>
