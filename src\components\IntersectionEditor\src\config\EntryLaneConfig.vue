<template>
  <div class="entry-lane-config">
    <div class="lane-header">
      <h4>进口车道配置</h4>
      <div class="lane-actions">
        <el-button type="primary" size="small" @click="applyChanges">应用更改</el-button>
      </div>
    </div>

    <div class="lane-table-container">
      <el-table :data="laneData" border stripe>
        <el-table-column label="车道序号" width="100">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="直行" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.nThrough" :true-label="1" :false-value="0" />
          </template>
        </el-table-column>

        <el-table-column label="左转" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.nTurnLeft" :true-label="1" :false-value="0" />
          </template>
        </el-table-column>

        <el-table-column label="右转" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.nTurnRight" :true-label="1" :false-value="0" />
          </template>
        </el-table-column>

        <el-table-column label="掉头" width="100">
          <template #default="{ row }">
            <el-checkbox v-model="row.nTurnAround" :true-label="1" :false-value="0" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="lane-tips">
      <p>提示：每个车道至少选择一个方向，最多选择两个方向</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { Road, Lane } from '../types';

// 定义属性
const props = defineProps<{
  selectedRoad: Road;
  roadId: number | null;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'change', data: { roadId: number, lanes: Lane[] }): void;
}>();

// 车道数据
const laneData = ref<Lane[]>([]);

// 监听选中的道路变化
watch(() => props.selectedRoad, (newRoad) => {
  if (newRoad && newRoad.approach && newRoad.approach.lanes) {
    // 深拷贝车道数据，避免直接修改原始数据
    laneData.value = JSON.parse(JSON.stringify(newRoad.approach.lanes));
  } else {
    laneData.value = [];
  }
}, { immediate: true, deep: true });

// 验证车道配置
const validateLanes = (): boolean => {
  for (let i = 0; i < laneData.value.length; i++) {
    const lane = laneData.value[i];
    const directionCount = lane.nThrough + lane.nTurnLeft + lane.nTurnRight + lane.nTurnAround;

    if (directionCount === 0) {
      ElMessage.error(`车道 ${i + 1} 至少需要选择一个方向`);
      return false;
    }

    if (directionCount > 2) {
      ElMessage.error(`车道 ${i + 1} 最多只能选择两个方向`);
      return false;
    }
  }

  return true;
};

// 应用更改
const applyChanges = (): void => {
  if (!validateLanes()) return;

  if (props.roadId) {
    emit('change', {
      roadId: props.roadId,
      lanes: [...laneData.value]
    });
  }
};
</script>

<style scoped>
.entry-lane-config {
  padding: 10px;
}

.lane-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.lane-header h4 {
  margin: 0;
  font-size: 16px;
}

.lane-table-container {
  margin-bottom: 15px;
}

.lane-tips {
  font-size: 12px;
  color: #909399;
}
</style>
