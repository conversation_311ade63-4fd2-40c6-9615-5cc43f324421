<template>
    <el-row :gutter="80">
        <el-col :span="6" v-for="item in stageList" :key="item.id">
            <el-card style="width: 360px;">
                <template #header>
                    <div class="card-header">
                        <span>阶段{{ item.id }}</span>
                    </div>
                </template>
                <el-form label-position="left" :model="form" label-width="auto" style="width: 300px">
                    <el-form-item label="绿信比">
                        <el-input v-model="form.name" />
                    </el-form-item>
                    <el-form-item label="相位">
                        <el-select v-model="form.region" placeholder="请选择相位">
                            <el-option label="Zone one" value="shanghai" />
                            <el-option label="Zone two" value="beijing" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="绿灯">
                        <el-input v-model="form.name" />
                    </el-form-item>
                    <el-form-item label="阶段号">
                        <el-input v-model="form.name" />
                    </el-form-item>
                    <el-form-item label="黄灯">
                        <el-input v-model="form.name" />
                    </el-form-item>
                    <el-form-item label="红灯清空">
                        <el-input v-model="form.name" />
                    </el-form-item>
                </el-form>
            </el-card>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

// do not use same name with ref
const form = reactive({
    name: '',
    region: '',
    date1: '',
    date2: '',
    delivery: false,
    type: [],
    resource: '',
    desc: '',
})

const stageList = ref([
    {
        id: 1,
        greenSignalRatio: '30',
        phase: 'phase',
        green: 'green',
        stageNo: '1',
        yellow: 'yellow',
        redClean: 'redClean'
    },
    {
        id: 2,
        greenSignalRatio: '30',
        phase: 'phase',
        green: 'green',
        stageNo: '1',
        yellow: 'yellow',
        redClean: 'redClean'
    },
])
</script>

<style scoped>
.card-header {
    font-size: 14px;
    text-align: center;
}
</style>