export interface Phase {
  id: number;
  direction: number[];
  peddirection: number[];
  mingreen: number;
  max1: number;
  max2: number;
  passage: number;
  phasewalk: number;
  pedclear: number;
  yellow: number;
  redclear: number;
  flashgreen: number;
  redyellow: number;
  ring: number;
  greenpulse: number;
  redpulse: number;
  pedgreenpulse: number;
  gapout: number;
  vehiclethresh: number;
  pedestrianthresh: number;
  linkphase: string | number;
  controltype: number;
  concurrent: number[];
  pulsetype?: number;
  newring?: number;
  newconcurrent?: number[];
}
