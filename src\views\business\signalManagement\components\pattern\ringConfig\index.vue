<template>
    <el-row :gutter="470">
        <el-col :span="6" v-for="indexI in 2" :key="indexI">
            <el-card style="width: 460px">
                <template #header>
                    <div class="card-header">
                        <span>环{{ indexI }}</span>
                    </div>
                </template>
                <div class="form-title">
                    <div>相位</div>
                    <div>绿信比</div>
                    <div>模式</div>
                </div>
                <div class="form-content" v-for="index in 4" :key="index">
                    <div>
                        <el-select v-model="form.phase" placeholder="请选择相位">
                            <el-option label="Zone one" value="shanghai" />
                            <el-option label="Zone two" value="beijing" />
                        </el-select>
                    </div>
                    <div>
                        <el-input v-model="form.greenSignalRatio" />
                    </div>
                    <div>
                        <el-select v-model="form.mode" placeholder="请选择模式">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

// do not use same name with ref
const form = reactive({
    greenSignalRatio: '30',
    phase: '',
    mode: '无',
})

const options = [
    {
        value: '其他',
        label: '其他',
    },
    {
        value: '无',
        label: '无',
    },
    {
        value: '最小绿请求',
        label: '最小绿请求',
    },
    {
        value: '最大绿请求',
        label: '最大绿请求',
    },
    {
        value: '行人请求',
        label: '行人请求',
    },
    {
        value: '最大绿和行人请求',
        label: '最大绿和行人请求',
    },
    {
        value: '忽略相位',
        label: '忽略相位',
    },
    {
        value: '关断相位',
        label: '关断相位',
    },
    {
        value: '黄闪相位',
        label: '黄闪相位',
    },
    {
        value: '非感应',
        label: '非感应',
    },
    {
        value: '驻留',
        label: '驻留',
    },
]
</script>

<style scoped>
.card-header {
    font-size: 14px;
    text-align: center;
}

.form-title {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    /* 添加间距 */
    margin-bottom: 10px;
}

.form-title>div {
    flex: 1;
    text-align: center;
    font-weight: bold;
}

.form-content {
    display: flex;
    gap: 20px;
    /* 与标题保持一致间距 */
    margin-bottom: 15px;
}

.form-content>div {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 保持表单元素宽度自适应 */
.form-content .el-select,
.form-content .el-input {
    width: 100%;
}
</style>