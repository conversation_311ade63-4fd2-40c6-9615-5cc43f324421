<template>
    <!-- 操作按钮区域 -->
    <div class="tab-operations">
        <el-button type="primary" @click="handleRenameTab" :disabled="!editableTabsValue">
            <el-icon>
                <Edit />
            </el-icon>
            修改当前计划名称
        </el-button>
    </div>

    <!-- Tab组件 -->
    <el-tabs v-model="editableTabsValue" type="card" editable class="plan-tabs" @edit="handleTabsEdit">
        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
            <PlanTab v-if="item.tableData && item.name && item.title" :plan="item.tableData"
                :planid="parseInt(item.name)" :planname="item.title" :coordinate="0" />
        </el-tab-pane>
    </el-tabs>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/modules/globalParam'

import type { TabPaneName } from 'element-plus'
import PlanTab from './planTab/index.vue';

// 使用 Pinia store
const globalStore = useGlobalStore()
const { tscParam } = storeToRefs(globalStore)

let tabIndex = 0
const editableTabsValue = ref('1')

// 从 Pinia store 获取计划数据
const editableTabs = computed(() => {
    try {
        const planList = tscParam.value?.planList || []
        return planList.map((planItem: any) => ({
            title: planItem.desc || `计划${planItem.id}`,
            name: planItem.id.toString(),
            tableData: planItem.plan || []
        }))
    } catch (error) {
        console.error('Error accessing planList from store:', error)
        return []
    }
})

const handleTabsEdit = (
    targetName: TabPaneName | undefined,
    action: 'remove' | 'add'
) => {
    if (action === 'add') {
        // 添加新计划到 Pinia store
        const newPlanId = ++tabIndex
        const newPlan = {
            id: newPlanId,
            desc: `计划${newPlanId}`,
            plan: []
        }

        // 更新 Pinia store
        if (tscParam.value && tscParam.value.planList) {
            tscParam.value.planList.push(newPlan)
        }

        editableTabsValue.value = newPlanId.toString()
    } else if (action === 'remove') {
        // 从 Pinia store 删除计划
        if (tscParam.value && tscParam.value.planList) {
            const targetId = parseInt(targetName as string)
            const planIndex = tscParam.value.planList.findIndex((plan: any) => plan.id === targetId)

            if (planIndex > -1) {
                // 处理激活tab的切换
                let activeName = editableTabsValue.value
                if (activeName === targetName) {
                    const plans = tscParam.value.planList
                    const nextPlan = plans[planIndex + 1] || plans[planIndex - 1]
                    if (nextPlan) {
                        activeName = nextPlan.id.toString()
                    }
                }

                // 删除计划
                tscParam.value.planList.splice(planIndex, 1)
                editableTabsValue.value = activeName
            }
        }
    }
}

// 修改当前Tab名称的函数
const handleRenameTab = async () => {
    if (!editableTabsValue.value) {
        ElMessage.warning('请先选择一个计划')
        return
    }

    // 找到当前激活的计划
    const currentPlanId = parseInt(editableTabsValue.value)
    const currentPlan = tscParam.value?.planList?.find((plan: any) => plan.id === currentPlanId)

    if (!currentPlan) {
        ElMessage.error('未找到当前计划')
        return
    }

    try {
        const { value: newName } = await ElMessageBox.prompt(
            '请输入新的计划名称',
            '修改计划名称',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValue: currentPlan.desc,
                inputValidator: (value: string) => {
                    if (!value || value.trim() === '') {
                        return '计划名称不能为空'
                    }

                    // 检查是否与其他计划名称重复
                    const trimmedValue = value.trim()
                    const isDuplicate = tscParam.value?.planList?.some(
                        (plan: any) => plan.id !== currentPlanId && plan.desc === trimmedValue
                    )

                    if (isDuplicate) {
                        return '计划名称不能重复'
                    }

                    return true
                },
                inputErrorMessage: '请输入有效的计划名称'
            }
        )

        // 更新计划名称到 Pinia store
        currentPlan.desc = newName.trim()
    } catch (error) {
        // 用户取消操作，不需要处理
    }
}
</script>

<style>
.tab-operations {
    margin-bottom: 10px;
}

.tab-operations .el-button {
    margin-right: 12px;
}

.plan-tabs>.el-tabs__content {
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}
</style>