<template>
    <!-- 操作按钮区域 -->
    <div class="tab-operations">
        <el-button type="primary" @click="handleRenameTab" :disabled="!editableTabsValue">
            <el-icon>
                <Edit />
            </el-icon>
            修改当前计划名称
        </el-button>
    </div>

    <!-- Tab组件 -->
    <el-tabs v-model="editableTabsValue" type="card" editable class="plan-tabs" @edit="handleTabsEdit">
        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
            <PlanTab v-if="item.tableData && item.name && item.title" :plan="item.tableData"
                :planid="parseInt(item.name)" :planname="item.title" :coordinate="0"
                @add-item="handleAddItem(item, $event)" @delete-item="handleDeleteItem(item, $event)"
                @sort-plan="handleSortPlan(item, $event)" />
        </el-tab-pane>
    </el-tabs>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'

import type { TabPaneName } from 'element-plus'
import PlanTab from './planTab/index.vue';

// 定义接口类型
interface PlanItem {
    id: number
    hour: number
    minute: number | string
    control: number
    pattern?: number
}

interface TabData {
    title: string
    name: string
    tableData: PlanItem[]
}

let tabIndex = 0
const editableTabsValue = ref('1')
const editableTabs = ref<TabData[]>([])

const handleTabsEdit = (
    targetName: TabPaneName | undefined,
    action: 'remove' | 'add'
) => {
    if (action === 'add') {
        const newTabName = `${++tabIndex}`
        editableTabs.value.push({
            title: `计划${tabIndex}`,
            name: newTabName,
            tableData: [] // 新Tab默认为空，用户可以通过添加按钮添加数据
        })
        editableTabsValue.value = newTabName
    } else if (action === 'remove') {
        const tabs = editableTabs.value
        let activeName = editableTabsValue.value
        if (activeName === targetName) {
            tabs.forEach((tab, index) => {
                if (tab.name === targetName) {
                    const nextTab = tabs[index + 1] || tabs[index - 1]
                    if (nextTab) {
                        activeName = nextTab.name
                    }
                }
            })
        }

        editableTabsValue.value = activeName
        editableTabs.value = tabs.filter((tab) => tab.name !== targetName)
    }
}

// 修改当前Tab名称的函数
const handleRenameTab = async () => {
    if (!editableTabsValue.value) {
        ElMessage.warning('请先选择一个计划')
        return
    }

    // 找到当前激活的tab
    const currentTab = editableTabs.value.find(tab => tab.name === editableTabsValue.value)
    if (!currentTab) {
        ElMessage.error('未找到当前计划')
        return
    }

    try {
        const { value: newName } = await ElMessageBox.prompt(
            '请输入新的计划名称',
            '修改计划名称',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputValue: currentTab.title,
                inputValidator: (value: string) => {
                    if (!value || value.trim() === '') {
                        return '计划名称不能为空'
                    }

                    // 检查是否与其他tab名称重复
                    const trimmedValue = value.trim()
                    const isDuplicate = editableTabs.value.some(
                        tab => tab.name !== currentTab.name && tab.title === trimmedValue
                    )

                    if (isDuplicate) {
                        return '计划名称不能重复'
                    }

                    return true
                },
                inputErrorMessage: '请输入有效的计划名称'
            }
        )

        // 更新tab名称
        currentTab.title = newName.trim()
        ElMessage.success('计划名称修改成功')

    } catch (error) {
        // 用户取消操作，不需要处理
    }
}

// 处理子组件添加项目事件
const handleAddItem = (tabData: TabData, newItem: PlanItem) => {
    tabData.tableData.push(newItem)
}

// 处理子组件删除项目事件
const handleDeleteItem = (tabData: TabData, index: number) => {
    tabData.tableData.splice(index, 1)
}

// 处理子组件排序事件
const handleSortPlan = (tabData: TabData, sortedPlan: PlanItem[]) => {
    tabData.tableData = sortedPlan
}
</script>

<style>
.tab-operations {
    margin-bottom: 10px;
}

.tab-operations .el-button {
    margin-right: 12px;
}

.plan-tabs>.el-tabs__content {
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}
</style>