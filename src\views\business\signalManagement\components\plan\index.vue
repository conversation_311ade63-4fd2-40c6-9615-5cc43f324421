<template>
    <el-tabs v-if="isTabsReady" v-model="editableTabsValue" type="card" editable class="plan-tabs"
        @edit="handleTabsEdit">
        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
            <PlanTab v-if="item.tableData && item.name && item.title" :plan="item.tableData"
                :planid="parseInt(item.name)" :planname="item.title" :coordinate="0" />
        </el-tab-pane>
    </el-tabs>
    <div v-else class="loading-placeholder">
        <el-skeleton :rows="5" animated />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'

import type { TabPaneName } from 'element-plus'
import PlanTab from './planTab/index.vue';

// 定义接口类型
interface PlanItem {
    id: number
    hour: number
    minute: number | string
    control: number
    pattern?: number
}

interface TabData {
    title: string
    name: string
    tableData: PlanItem[]
}

let tabIndex = 2
const isTabsReady = ref(false)
const editableTabsValue = ref('1')
const editableTabs = ref<TabData[]>([
    {
        title: '计划1',
        name: '1',
        tableData: [
            {
                id: 1,
                hour: 0,
                minute: 0,
                control: 5,
                pattern: 1,
            },
            {
                id: 2,
                hour: 0,
                minute: 0,
                control: 5,
                pattern: 2,
            },
        ]
    },
    {
        title: '计划2',
        name: '2',
        tableData: [
            {
                id: 3,
                hour: 0,
                minute: 0,
                control: 5,
            },
        ]
    },
])

// 初始化组件
onMounted(async () => {
    await nextTick()
    isTabsReady.value = true
})

const handleTabsEdit = (
    targetName: TabPaneName | undefined,
    action: 'remove' | 'add'
) => {
    if (action === 'add') {
        const newTabName = `${++tabIndex}`
        editableTabs.value.push({
            title: `计划${tabIndex}`,
            name: newTabName,
            tableData: [
                {
                    id: tabIndex * 10,
                    hour: 0,
                    minute: 0,
                    control: 5,
                },
            ]
        })
        editableTabsValue.value = newTabName
    } else if (action === 'remove') {
        const tabs = editableTabs.value
        let activeName = editableTabsValue.value
        if (activeName === targetName) {
            tabs.forEach((tab, index) => {
                if (tab.name === targetName) {
                    const nextTab = tabs[index + 1] || tabs[index - 1]
                    if (nextTab) {
                        activeName = nextTab.name
                    }
                }
            })
        }

        editableTabsValue.value = activeName
        editableTabs.value = tabs.filter((tab) => tab.name !== targetName)
    }
}
</script>

<style>
.plan-tabs>.el-tabs__content {
    /* padding: 32px; */
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.loading-placeholder {
    padding: 20px;
    min-height: 400px;
}
</style>