<template>
    <el-tabs v-model="editableTabsValue" type="card" editable class="plan-tabs" @edit="handleTabsEdit">
        <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name">
            <!-- <div class="mb-2">
                <el-button type="primary" @click="handleAdd">添加</el-button>
                <el-button type="primary" @click="handleAdd">编辑计划名</el-button>
                <el-button type="primary" @click="handleAdd">一键排序</el-button>
            </div>
            <el-table :data="item.tableData" :border="parentBorder" :preserve-expanded-content="preserveExpanded"
                style="width: 100%">
                <el-table-column label="时间" prop="time">
                    <template #default="scope">
                        <el-time-select v-model="scope.row.time" start="00:00" step="00:30"
                            end="23:59" placeholder="请选择时间" format="HH:mm" />
                    </template>
</el-table-column>
<el-table-column label="控制方式" prop="controlMode">
    <template #default="scope">
                        <el-select v-model="scope.row.controlMode" placeholder="请选择控制方式">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
</el-table-column>
<el-table-column label="方案" prop="pattern">
    <template #default="scope">
                        <el-select v-model="scope.row.pattern" placeholder="请选择方案">
                            <el-option v-for="item in 10" :key="item" :label="item"
                                :value="item" />
                        </el-select>
                    </template>
</el-table-column>
<el-table-column label="周期" prop="cycle">
</el-table-column>
<el-table-column fixed="right" label="操作" width="100">
    <template #default="scope">
                        <el-button link type="danger" size="small" @click.prevent="handleDelete(scope.$index)">
                            删除
                        </el-button>
                    </template>
</el-table-column>
</el-table> -->
            <PlanTab :plan="item.tableData" :planid="parseInt(item.name)" :planname="item.title" :coordinate="0" />
        </el-tab-pane>
    </el-tabs>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

import type { TabPaneName } from 'element-plus'
import PlanTab from './planTab/index.vue';

let tabIndex = 2
const parentBorder = ref(false)
const preserveExpanded = ref(false)
const editableTabsValue = ref('1')
const editableTabs = ref([
    {
        title: '计划1',
        name: '1',
        tableData: [
            {
                time: '00:00',
                controlMode: '定周期控制',
                pattern: '1',
                cycle: '120',
            },
            {
                time: '00:00',
                controlMode: '定周期控制',
                pattern: '2',
                cycle: '120',
            },
        ]
    },
    {
        title: '计划2',
        name: '2',
        tableData: [
            {
                time: '00:00',
                controlMode: '定周期控制',
                pattern: '',
                cycle: ''
            },
        ]
    },
])

const handleTabsEdit = (
    targetName: TabPaneName | undefined,
    action: 'remove' | 'add'
) => {
    if (action === 'add') {
        const newTabName = `${++tabIndex}`
        editableTabs.value.push({
            title: '计划3',
            name: newTabName,
            tableData: [
                {
                    time: '00:00',
                    controlMode: '定周期控制',
                    pattern: '',
                    cycle: ''
                },
            ]
        })
        editableTabsValue.value = newTabName
    } else if (action === 'remove') {
        const tabs = editableTabs.value
        let activeName = editableTabsValue.value
        if (activeName === targetName) {
            tabs.forEach((tab, index) => {
                if (tab.name === targetName) {
                    const nextTab = tabs[index + 1] || tabs[index - 1]
                    if (nextTab) {
                        activeName = nextTab.name
                    }
                }
            })
        }

        editableTabsValue.value = activeName
        editableTabs.value = tabs.filter((tab) => tab.name !== targetName)
    }
}

const options = [
    {
        value: '定周期控制',
        label: '定周期控制',
    },
    {
        value: '无电缆控制',
        label: '无电缆控制',
    },
    {
        value: '自适应',
        label: '自适应',
    },
    {
        value: '黄闪',
        label: '黄闪',
    },
    {
        value: '全红',
        label: '全红',
    },
    {
        value: '关灯',
        label: '关灯',
    },
    {
        value: '方案生成',
        label: '方案生成',
    },
    {
        value: '行人过街控制',
        label: '行人过街控制',
    },
    {
        value: '感应式自适应',
        label: '感应式自适应',
    },
    {
        value: '感应式行人过街控制',
        label: '感应式行人过街控制',
    },
]

function handleAdd() {
    console.log("获取当前 Tab 后添加数据")
}

function handleDelete(index: number) {
    console.log("删除当前行")
}
</script>

<style>
.plan-tabs>.el-tabs__content {
    /* padding: 32px; */
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}
</style>