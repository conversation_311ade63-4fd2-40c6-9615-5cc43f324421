import GlobalParamsMgr from '@/components/EdgeMgr/controller/globalParamsMgr'

// 全局参数管理器实例
let globalParamsMgr: GlobalParamsMgr | null = null

/**
 * 获取全局参数管理器实例
 * @returns GlobalParamsMgr实例
 */
export function getGlobalParamsMgr(): GlobalParamsMgr {
  if (!globalParamsMgr) {
    globalParamsMgr = new GlobalParamsMgr()
    globalParamsMgr.Init()
  }
  return globalParamsMgr
}

/**
 * 初始化全局管理器
 * 在应用启动时调用
 */
export function initGlobalManager(): void {
  getGlobalParamsMgr()
  console.log('Global manager initialized')
}
