<template>
  <div class="type-selector">
    <el-form label-position="top">
      <el-form-item label="路口类型">
        <el-select v-model="selectedType" class="type-select" @change="handleTypeChange">
          <el-option
            v-for="(name, type) in typeOptions"
            :key="type"
            :label="name"
            :value="type"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import { INTERSECTION_TYPE_NAMES } from '../types';

// 定义属性
const props = defineProps<{
  modelValue: string;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

// 路口类型选项
const typeOptions = INTERSECTION_TYPE_NAMES;

// 选中的类型
const selectedType = ref<string>(props.modelValue || 'cross');

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== selectedType.value) {
    selectedType.value = newValue;
  }
});

// 处理类型变更
const handleTypeChange = (value: string): void => {
  emit('update:modelValue', value);
};
</script>

<style scoped>
.type-selector {
  width: 100%;
}

.type-select {
  width: 100%;
}
</style>