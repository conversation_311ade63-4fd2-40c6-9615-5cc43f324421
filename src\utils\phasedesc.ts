// 相位描述接口
interface PhaseDescription {
  id: number;
  name: string;
}

/**
 * @Description: 根据相位的描述id获取对应的描述名字
 * @param list 相位ID数组
 * @returns 相位描述字符串，多个描述用逗号分隔
 */
export function getPhaseDesc(list: number[]): string {
  const images: PhaseDescription[] = [{
    id: 1,
    name: '东直行'
  },
    {
      id: 2,
      name: '东左转'
    },
    {
      id: 3,
      name: '东右转'
    },
    {
      id: 4,
      name: '东掉头'
    },
    {
      id: 5,
      name: '西直行'
    },
    {
      id: 6,
      name: '西左转'
    },
    {
      id: 7,
      name: '西右转'
    },
    {
      id: 8,
      name: '西掉头'
    },
    {
      id: 9,
      name: '北直行'
    },
    {
      id: 10,
      name: '北左转'
    },
    {
      id: 11,
      name: '北右转'
    },
    {
      id: 12,
      name: '北掉头'
    },
    {
      id: 13,
      name: '南直行'
    },
    {
      id: 14,
      name: '南左转'
    },
    {
      id: 15,
      name: '南右转'
    },
    {
      id: 16,
      name: '南掉头'
    },
    {
      id: 17,
      name: '东南直行'
    },
    {
      id: 18,
      name: '东南左转'
    },
    {
      id: 19,
      name: '东南右转'
    },
    {
      id: 20,
      name: '东南掉头'
    },
    {
      id: 21,
      name: '西南直行'
    },
    {
      id: 22,
      name: '西南左转'
    },
    {
      id: 23,
      name: '西南右转'
    },
    {
      id: 24,
      name: '西南掉头'
    },
    {
      id: 25,
      name: '东北直行'
    },
    {
      id: 26,
      name: '东北左转'
    },
    {
      id: 27,
      name: '东北右转'
    },
    {
      id: 28,
      name: '东北掉头'
    },
    {
      id: 29,
      name: '西北直行'
    },
    {
      id: 30,
      name: '西北左转'
    },
    {
      id: 31,
      name: '西北右转'
    },
    {
      id: 32,
      name: '西北掉头'
    }
  ];

  let str: string = '';
  for (const ll of list) {
    for (const image of images) {
      if (image.id === ll) {
        str = str + ',' + image.name;
      }
    }
  }
  if (str !== '') {
    str = str.substring(1);
  }
  return str;
}
