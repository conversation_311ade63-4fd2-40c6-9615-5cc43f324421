// 通道组件数据配置

import type { ChannelTypeOption, TurnOption, TurnListItem, SignLocationOption, TypeOption } from './types';

// 通道类型选项
export const channelTypeOptions: ChannelTypeOption[] = [
  {
    value: 0,
    label: '机动车'
  },
  {
    value: 1,
    label: '非机动车'
  },
  {
    value: 2,
    label: '行人'
  },
  {
    value: 3,
    label: '公交车'
  },
  {
    value: 4,
    label: '其他'
  }
];

// 车辆转向选项
export const carList: TurnOption[] = [
  {
    value: 0,
    label: '直行'
  },
  {
    value: 1,
    label: '左转'
  },
  {
    value: 2,
    label: '右转'
  },
  {
    value: 3,
    label: '掉头'
  },
  {
    value: 4,
    label: '待转'
  }
];

// 行人转向选项
export const peopleList: TurnOption[] = [
  {
    value: 4,
    label: '东行人'
  },
  {
    value: 5,
    label: '西行人'
  },
  {
    value: 6,
    label: '南行人'
  },
  {
    value: 7,
    label: '北行人'
  }
];

// 转向列表配置
export const turnList: TurnListItem[] = [
  {
    label: '机动车',
    children: [
      {
        value: 0,
        label: '直行'
      },
      {
        value: 1,
        label: '左转'
      },
      {
        value: 2,
        label: '右转'
      },
      {
        value: 3,
        label: '掉头'
      },
      {
        value: 4,
        label: '待转'
      }
    ]
  },
  {
    label: '机动车（左转专用）',
    children: [
      {
        value: 0,
        label: '直行'
      },
      {
        value: 1,
        label: '左转'
      },
      {
        value: 2,
        label: '右转'
      },
      {
        value: 3,
        label: '掉头'
      },
      {
        value: 4,
        label: '待转'
      }
    ]
  },
  {
    label: '行人',
    children: [
      {
        value: 4,
        label: '东行人'
      },
      {
        value: 5,
        label: '西行人'
      },
      {
        value: 6,
        label: '南行人'
      },
      {
        value: 7,
        label: '北行人'
      }
    ]
  },
  {
    label: '机动车（右转专用）',
    children: [
      {
        value: 0,
        label: '直行'
      },
      {
        value: 1,
        label: '左转'
      },
      {
        value: 2,
        label: '右转'
      },
      {
        value: 3,
        label: '掉头'
      },
      {
        value: 4,
        label: '待转'
      }
    ]
  },
  {
    label: '机动车（掉头专用）',
    children: [
      {
        value: 0,
        label: '直行'
      },
      {
        value: 1,
        label: '左转'
      },
      {
        value: 2,
        label: '右转'
      },
      {
        value: 3,
        label: '掉头'
      },
      {
        value: 4,
        label: '待转'
      }
    ]
  }
];

// 信号灯位置选项
export const signLocationList: SignLocationOption[] = [
  {
    value: 0,
    label: '东'
  },
  {
    value: 1,
    label: '西'
  },
  {
    value: 2,
    label: '南'
  },
  {
    value: 3,
    label: '北'
  },
  {
    value: 4,
    label: '东南'
  },
  {
    value: 5,
    label: '西南'
  },
  {
    value: 6,
    label: '东北'
  },
  {
    value: 7,
    label: '西北'
  }
];

// 基础类型选项
export const baseTypeOptions: TypeOption[] = [
  {
    value: 1,
    label: '其他'
  },
  {
    value: 2,
    label: '机动车相位'
  },
  {
    value: 3,
    label: '行人相位'
  },
  {
    value: 4,
    label: '跟随相位'
  }
];

// 默认通道初始化数据
export const defaultChannelInitData = {
  desc: '',
  controlsource: 1,
  controltype: 2,
  typeAndSouce: [2, 1],
  voltthresh: 50,
  pacthresh: 30,
  peakhthresh: 400,
  peaklthresh: 130
};

// 默认样式数据
export const defaultStyleData = {
  left: '60px',
  top: '10px'
};
