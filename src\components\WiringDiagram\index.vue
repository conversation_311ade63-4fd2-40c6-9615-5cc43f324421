<!-- 通道接线图 -->
<template>
  <svg-icon :icon-class="svgPath" className="wiringImg"></svg-icon>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue'
import { getTheme } from '@/utils/theme'

export default defineComponent({
  name: 'WiringDiagram',
  setup() {
    const svgPath = ref<string>('wiringLightZh')

    onMounted(() => {
      if (getTheme() === 'dark') {
        svgPath.value = 'wiringDarkZh'
      } else {
        svgPath.value = 'wiringLightZh'
      }
    })

    return {
      svgPath
    }
  }
})
</script>
<style lang="scss" scoped>
  .wiringImg {
    width: 710px;
    height: 710px;
  }
</style>
