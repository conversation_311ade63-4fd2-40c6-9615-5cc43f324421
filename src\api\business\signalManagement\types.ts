export interface SignalManagementVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 信号机名称
   */
  signalName: string;

  /**
   * 信号机IP
   */
  signalIp: string;

  /**
   * 信号机端口
   */
  signalPort: number;

  /**
   * 信号机协议
   */
  signalProtocol: string;

  /**
   * 信号机厂家
   */
  signalVender: string;

  /**
   * 信号机配置
   */
  signalConfig: object;

  /**
   * 信号机数据
   */
  data: object;

  /**
   * 信号机环
   */
  ring: object;

  /**
   * 信号机指令数据
   */
  commondata: object;

  /**
   * 信号机合并方向
   */
  mergedirection: object;

}

export interface SignalManagementForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 信号机名称
   */
  signalName?: string;

  /**
   * 信号机IP
   */
  signalIp?: string;

  /**
   * 信号机端口
   */
  signalPort?: number;

  /**
   * 信号机协议
   */
  signalProtocol?: string;

  /**
   * 信号机厂家
   */
  signalVender?: string;

  /**
   * 信号机配置
   */
  signalConfig?: object;

  /**
   * 信号机数据
   */
  data?: object;

  /**
   * 信号机环
   */
  ring?: object;

  /**
   * 信号机指令数据
   */
  commondata?: object;

  /**
   * 信号机合并方向
   */
  mergedirection?: object;

}

export interface SignalManagementQuery extends PageQuery {

  /**
   * 信号机名称
   */
  signalName?: string;

  /**
   * 信号机IP
   */
  signalIp?: string;

  /**
   * 信号机协议
   */
  signalProtocol?: string;

  /**
   * 信号机厂家
   */
  signalVender?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}



