<template>
    <div class="mb-2">
        <el-button type="primary" @click="handleAdd">添加</el-button>
    </div>
    <el-table :data="tableData" :border="parentBorder" :preserve-expanded-content="preserveExpanded"
        style="width: 100%">
        <el-table-column label="描述" prop="desc">
            <template #default="scope">
                <div @dblclick="startEdit(scope.row, 'desc')" v-if="!scope.row.isEditing">
                    {{ scope.row.desc }}
                </div>
                <el-input v-else v-model="scope.row.desc" @blur="saveEdit" @keyup.enter="saveEdit" />
            </template>
        </el-table-column>
        <el-table-column label="月份" prop="month">
            <template #default="scope">
                <el-select v-model="scope.row.month" placeholder="请选择月份">
                    <el-option v-for="item in monthOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column label="周" prop="week">
            <template #default="scope">
                <el-select v-model="scope.row.week" placeholder="请选择周">
                    <el-option v-for="item in weekOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column label="日期" prop="date">
            <template #default="scope">
                <el-select v-model="scope.row.date" placeholder="请选择日期">
                    <el-option v-for="item in 31" :key="item" :label="item" :value="item.toString()" />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column label="计划" prop="plan">
            <template #default="scope">
                <el-select v-model="scope.row.plan" placeholder="请选择计划">
                    <el-option v-for="item in 10" :key="item" :label="item" :value="item" />
                </el-select>
            </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
                <el-button link type="danger" size="small" @click.prevent="handleDelete(scope.$index)">
                    删除
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const editingCell = ref(null)
const parentBorder = ref(false)
const preserveExpanded = ref(false)
const tableData = ref([
    {
        desc: '日计划1',
        month: '1',
        week: '1',
        date: '1',
        plan: '1',
    },
    {
        desc: '日计划2',
        month: '2',
        week: '2',
        date: '1',
        plan: '1',
    },
])

const monthOptions = [
    {
        value: '1',
        label: '1月',
    },
    {
        value: '2',
        label: '2月',
    },
    {
        value: '3',
        label: '3月',
    },
    {
        value: '4',
        label: '4月',
    },
    {
        value: '5',
        label: '5月',
    },
    {
        value: '6',
        label: '6月',
    },
    {
        value: '7',
        label: '7月',
    },
    {
        value: '8',
        label: '8月',
    },
    {
        value: '9',
        label: '9月',
    },
    {
        value: '10',
        label: '10月',
    },
    {
        value: '11',
        label: '11月',
    },
    {
        value: '12',
        label: '12月',
    },
]

const weekOptions = [
    {
        value: '1',
        label: '星期一',
    },
    {
        value: '2',
        label: '星期二',
    },
    {
        value: '3',
        label: '星期三',
    },
    {
        value: '4',
        label: '星期四',
    },
    {
        value: '5',
        label: '星期五',
    },
    {
        value: '6',
        label: '星期六',
    },
    {
        value: '7',
        label: '星期日',
    },
]

function handleAdd() {
    tableData.value.push({
        desc: '日计划',
        month: '',
        week: '',
        date: '',
        plan: '',
    })
}

function handleDelete(index: number) {
    tableData.value.splice(index, 1)
}

const startEdit = (row, field) => {
    // 防止重复点击同一个单元格
    if (row.isEditing && row.editingField === field) return

    // 保存当前编辑的字段
    row.editingField = field
    row.isEditing = true

    // 记录当前编辑的单元格引用
    editingCell.value = row
}

const saveEdit = () => {
    if (!editingCell.value) return

    // 结束编辑状态
    editingCell.value.isEditing = false
    editingCell.value.editingField = null
    editingCell.value = null
}
</script>

<style>
.plan-tabs>.el-tabs__content {
    /* padding: 32px; */
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}
</style>