<template>
    <div class="app-container data-container" ref="dateContainerRef">
        <el-button style="margin-bottom: 10px" type="primary" @click="onAdd">
            添加
        </el-button>
        <el-table :data="dateList" row-key="id" :max-height="tableHeight" id="footerBtn">
            <el-table-column label="描述" min-width="100" align="center">
                <template #default="scope">
                    <el-input size="small" v-model="scope.row.desc" />
                </template>
            </el-table-column>
            <el-table-column label="月份" align="center" min-width="100">
                <template #default="scope">
                    <el-select v-model="scope.row.month" multiple collapse-tags placeholder="请选择" size="small"
                        @change="handleMonth(scope.row.month, scope.$index)"
                        @visible-change="initOldOptions(scope.row.month, $event)">
                        <el-option v-for="item in months" :key="item.label" :label="item.label" :value="item.value" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="周" align="center" min-width="100">
                <template #default="scope">
                    <el-select v-model="scope.row.day" multiple collapse-tags placeholder="请选择" size="small"
                        @change="handleDay(scope.row.day, scope.$index)"
                        @visible-change="initOldOptions(scope.row.day, $event)">
                        <el-option v-for="item in days" :key="item.label" :label="item.label" :value="item.value" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="日期" align="center" min-width="100">
                <template #default="scope">
                    <el-select v-model="scope.row.date" multiple collapse-tags placeholder="请选择" size="small"
                        @change="handleDate(scope.row.date, scope.$index)"
                        @visible-change="initOldOptions(scope.row.date, $event)">
                        <el-option v-for="item in dates" :key="item" :label="item" :value="item" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="计划" min-width="100" align="center">
                <template #default="scope">
                    <el-select v-model="scope.row.plan" placeholder="请选择" size="small">
                        <el-option v-for="item in planOptions" :key="item.id" :label="item.desc" :value="item.id" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="120">
                <template #default="scope">
                    <el-button type="danger" link @click="handleDelete(scope.$index)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia';
import { useGlobalStore } from '@/store/modules/globalParam';
import { getGlobalParamsMgr } from '@/components/EdgeMgr/globalManager';

// 定义接口类型
interface DateItem {
    id: number
    desc: string
    month: number[]
    day: number[]
    date: (string | number)[]
    plan: string | number
}

interface OptionItem {
    value: number
    label: string
}

interface PlanOption {
    id: number
    desc: string
}

// 组合式API - 使用 Pinia store
const globalStore = useGlobalStore()
const { tscParam } = storeToRefs(globalStore)

// 响应式数据
const dateContainerRef = ref()
const tableHeight = ref(760)
const id = ref(1)
const oldOptions = ref<(string | number)[]>([])

// 月份选项
const months: OptionItem[] = [
    { value: 0, label: '全选' },
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' },
    { value: 6, label: '6' },
    { value: 7, label: '7' },
    { value: 8, label: '8' },
    { value: 9, label: '9' },
    { value: 10, label: '10' },
    { value: 11, label: '11' },
    { value: 12, label: '12' }
]

// 星期选项
const days: OptionItem[] = [
    { value: 8, label: '全选' },
    { value: 0, label: '星期日' },
    { value: 1, label: '星期一' },
    { value: 2, label: '星期二' },
    { value: 3, label: '星期三' },
    { value: 4, label: '星期四' },
    { value: 5, label: '星期五' },
    { value: 6, label: '星期六' }
]

// 日期选项
const dates: (string | number)[] = [
    '全选', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31
]

// 计划选项
const planOptions = ref<PlanOption[]>([])

// 全局参数模型
let globalParamModel: any = null

// 计算属性 - 从 Pinia store 获取响应式数据
const dateList = computed(() => {
    try {
        return tscParam.value?.dateList || []
    } catch (error) {
        console.error('Error accessing dateList from store:', error)
        return []
    }
})

// 设置表格高度
const setTableHeight = () => {
    nextTick(() => {
        if (dateContainerRef.value) {
            tableHeight.value = dateContainerRef.value.offsetHeight - 90
            window.onresize = () => {
                if (dateContainerRef.value) {
                    tableHeight.value = dateContainerRef.value.offsetHeight - 90
                }
            }
        }
    })
}

// 初始化
const init = () => {
    initData()
    increaseId()
}

// 初始化数据
const initData = () => {
    try {
        planOptions.value = []

        if (!globalParamModel) {
            console.warn('globalParamModel is not available')
            return
        }

        const planList = globalParamModel.getParamsByType('planList') || []
        console.log('planList:', planList)
        const dateListData = globalParamModel.getParamsByType('dateList') || []

        // 初始化计划选项
        for (let i = 0; i < planList.length; i++) {
            const desc = planList[i]?.desc
            const id = planList[i]?.id
            if (desc === undefined || id === undefined) continue
            planOptions.value.push({ desc, id })
        }

        // 处理日期列表数据
        for (let i = 0; i < dateListData.length; i++) {
            if (dateListData[i]?.month && dateListData[i].month.length === 12) {
                dateListData[i].month.push(0)
            }
            if (dateListData[i]?.date && dateListData[i].date.length === 31) {
                dateListData[i].date.push('全选')
            }
            if (dateListData[i]?.day && dateListData[i].day.length === 7) {
                dateListData[i].day.push(8)
            }

            const plan = dateListData[i]?.plan
            const idList = planOptions.value.map(option => option.id)
            if (plan && !idList.includes(plan as number)) {
                dateListData[i].plan = ''
            }
        }
    } catch (error) {
        console.error('Error initializing data:', error)
    }
}

// 增加ID（寻找最小可用ID）
const increaseId = () => {
    try {
        // 从 Pinia store 获取数据
        const dateListData = tscParam.value?.dateList || []

        // 如果列表为空，直接使用ID 1
        if (dateListData.length === 0) {
            id.value = 1
            return
        }

        const dateIdList = dateListData.map((ele: DateItem) => ele.id)

        // 寻找最小的未使用ID
        for (let j = 1; j <= 255; j++) {
            if (!dateIdList.includes(j)) {
                id.value = j
                return
            }
        }

        // 如果所有ID都被使用了，使用最大ID + 1
        id.value = Math.max(...dateIdList) + 1
    } catch (error) {
        console.error('Error generating ID:', error)
        // 出错时使用随机ID
        id.value = Math.floor(Math.random() * 1000) + 1
    }
}

// 删除处理
const handleDelete = (index: number) => {
    ElMessageBox.confirm(
        '确认删除此日期？',
        '提示',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        try {
            // 同时更新 globalParamModel 和 Pinia store
            if (globalParamModel) {
                globalParamModel.deleteParamsByType('dateList', index, 1)
            }

            // // 直接更新 Pinia store
            // if (tscParam.value && tscParam.value.dateList) {
            //     tscParam.value.dateList.splice(index, 1)
            // }

            ElMessage({
                type: 'success',
                message: '删除成功'
            })
        } catch (error) {
            console.error('Error deleting item:', error)
            ElMessage.error('删除失败，请重试')
        }
    }).catch(() => {
        // ElMessage({
        //     type: 'info',
        //     message: '删除取消！'
        // })
    })
}

// 添加新项
const onAdd = () => {
    try {
        increaseId()

        // 检查当前数据长度
        const currentList = tscParam.value?.dateList || []
        if (currentList.length >= 40) {
            ElMessage.error('最多只能创建40条数据')
            return
        }

        const newDateItem: DateItem = {
            id: id.value,
            desc: `日期${id.value}`,
            month: [],
            day: [],
            date: [],
            plan: ''
        }

        // 同时更新 globalParamModel 和 Pinia store
        if (globalParamModel) {
            globalParamModel.addParamsByType('dateList', newDateItem)
        }

        // // 直接更新 Pinia store 以确保响应式更新
        // if (tscParam.value && tscParam.value.dateList) {
        //     tscParam.value.dateList.push(newDateItem)
        // }

        // ElMessage.success('添加成功')
    } catch (error) {
        console.error('Error adding date item:', error)
        ElMessage.error('添加失败，请重试')
    }
}

// 处理月份选择
const handleMonth = (val: number[], index: number) => {
    const allValues: number[] = []
    // 保留所有值
    for (const item of months) {
        allValues.push(item.value)
    }

    // 若是全部选择
    if (val.includes(0)) {
        dateList.value[index].month = allValues
    }
    // 取消全部选中，上次有，当前没有，表示取消全选
    else if (oldOptions.value.includes(0) && !val.includes(0)) {
        dateList.value[index].month = []
    }
    // 点击非全部选中，需要排除全部选中，以及，当前点击的选项
    // 新老数据都有全部选中
    else if (oldOptions.value.includes(0) && val.includes(0)) {
        const tempIndex = val.indexOf(0)
        val.splice(tempIndex, 1) // 排除全选选项
        dateList.value[index].month = val
    }
    // 全选未选，但是其他选项全部选上，则全选选上，上次和当前，都没有全选
    else if (!oldOptions.value.includes(0) && !val.includes(0)) {
        if (val.length === allValues.length - 1) {
            dateList.value[index].month = [0, ...val]
        }
    }

    oldOptions.value = dateList.value[index].month
}

// 处理日期选择
const handleDate = (val: (string | number)[], index: number) => {
    const allValues: (string | number)[] = []
    // 保留所有值
    for (const item of dates) {
        allValues.push(item)
    }

    // 若是全部选择
    if (val.includes('全选')) {
        dateList.value[index].date = allValues
    }
    // 取消全部选中，上次有，当前没有，表示取消全选
    else if (oldOptions.value.includes('全选') && !val.includes('全选')) {
        dateList.value[index].date = []
    }
    // 点击非全部选中，需要排除全部选中，以及，当前点击的选项
    // 新老数据都有全部选中
    else if (oldOptions.value.includes('全选') && val.includes('全选')) {
        const tempIndex = val.indexOf('全选')
        val.splice(tempIndex, 1) // 排除全选选项
        dateList.value[index].date = val
    }
    // 全选未选，但是其他选项全部选上，则全选选上，上次和当前，都没有全选
    else if (!oldOptions.value.includes('全选') && !val.includes('全选')) {
        if (val.length === allValues.length - 1) {
            dateList.value[index].date = ['全选', ...val]
        }
    }

    oldOptions.value = dateList.value[index].date
}

// 处理星期选择
const handleDay = (val: number[], index: number) => {
    const allValues: number[] = []
    // 保留所有值
    for (const item of days) {
        allValues.push(item.value)
    }

    // 若是全部选择
    if (val.includes(8)) {
        dateList.value[index].day = allValues
    }
    // 取消全部选中，上次有，当前没有，表示取消全选
    else if (oldOptions.value.includes(8) && !val.includes(8)) {
        dateList.value[index].day = []
    }
    // 点击非全部选中，需要排除全部选中，以及，当前点击的选项
    // 新老数据都有全部选中
    else if (oldOptions.value.includes(8) && val.includes(8)) {
        const tempIndex = val.indexOf(8)
        val.splice(tempIndex, 1) // 排除全选选项
        dateList.value[index].day = val
    }
    // 全选未选，但是其他选项全部选上，则全选选上，上次和当前，都没有全选
    else if (!oldOptions.value.includes(8) && !val.includes(8)) {
        if (val.length === allValues.length - 1) {
            dateList.value[index].day = [8, ...val]
        }
    }

    oldOptions.value = dateList.value[index].day
}

// 初始化旧选项
const initOldOptions = (val: (string | number)[], status: boolean) => {
    if (status) {
        oldOptions.value = val
    } else {
        oldOptions.value = []
    }
}

// 生命周期钩子
onMounted(() => {
    try {
        globalParamModel = getGlobalParamsMgr()
        if (globalParamModel) {
            init()
        }
        setTableHeight()
    } catch (error) {
        console.error('Daily component initialization error:', error)
    }
})
</script>

<style scoped lang="scss">
.checkbox-group {
    width: 200px;
}

.app-container {
    padding: 0px;
}

.data-container {
    height: 100%;
}

.tips {
    margin-bottom: 10px;
    color: #909399;
    font-size: 12px;
}

:deep(.el-table) {
    .el-select {
        width: 100%;
    }

    .el-input {
        width: 100%;
    }
}

:deep(.el-select__tags) {
    max-width: 100%;
}
</style>
