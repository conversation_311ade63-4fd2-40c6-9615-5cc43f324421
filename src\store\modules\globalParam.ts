import { defineStore } from 'pinia'
import  store  from '../index'

const tscParam = {
  phaseList: [],
  patternList: [],
  planList: [],
  dateList: [],
  overlaplList: [],
  channelList: [],
  detectorList: [],
  preemptList: [],
  channelGreenConflictInfo: [],
  channelCustom: [],
  pedestrainDetectorList: [],
  channellock: [],
  manualpanel: {
    mingreen: 15
  },
  singleoptim: [
  ]
}
let defaultCopiedTscParam = null
try {
  if (sessionStorage.tscParam) {
    defaultCopiedTscParam = JSON.parse(sessionStorage.tscParam)
  }
} catch (e) {
  console.log(e)
}


interface GlobalState {
  tscParam: any
  curPath: string
  copiedTscParam: any
  routers: any[]
  isfromatc: boolean
  curOpenatcAgentid: any
  curBodyWidth: number
  curBodyHeight: number
  hideMenu: boolean
  graphicMode: boolean
  roadDirection: string
  channelDescMap: Map<string, any>,
  curHomePage: string,
  isSetChannelization: boolean,
  isRefreshTankuang: string,
  associatedPhaseList: any[],
  PhaseDataMgr: any,
  sortPlanList: boolean
}

export const useGlobalStore = defineStore('global', {
  state: (): GlobalState => {
    return {
    tscParam: JSON.parse(JSON.stringify(tscParam)),
    curPath: '/overview/index',
    copiedTscParam: defaultCopiedTscParam,
    routers: [],
    isfromatc: false,
    curOpenatcAgentid: undefined, // 当前操作的设备，用于监听多设备切换时
    curBodyWidth: 1920,
    curBodyHeight: 1080,
    hideMenu: false,
    graphicMode: false, // 为true时，切换到图形界面模式，只显示路口图部分
    roadDirection: 'right', // 当前路口行车方向：默认右行
    channelDescMap: new Map(), // 管理实时通道描述数据
    // isShowGui: true, // 总览当前是否是图形界面
    curHomePage: 'Graphical', // 总览当前显示的界面：渠化界面 Channelization, 流量统计 Flowstatistics, 图形界面 Graphical, 文字界面 Text, 检测器模式 DetectorMode
    isSetChannelization: false, // 当前设备是否设置渠化图
    isRefreshTankuang: 'norefresh', // 刷新某个页面的相位弹框组件
    associatedPhaseList: [], // 关联相位列表
    PhaseDataMgr: {}, // 关联相位数据处理模型
    sortPlanList: false // 是否下载前排序一次计划表
    }
  },
  actions: {
    SaveTscParam (data) {
      // let customInfo = data.customInfo
      // data.customInfo = {
      //   ...tscParam.customInfo,
      //   ...customInfo
      // }
      this.tscParam = JSON.parse(JSON.stringify(data))
      // this.$patch({ tscParam: JSON.parse(JSON.stringify(data)) })
    },
    SaveSingleParam (param) {
      this.tscParam[param.type] = param.data
    },
    ResetTscParam () {
      this.tscParam = JSON.parse(JSON.stringify(tscParam))
    },
    SaveCurPath (path) {
      this.curPath = path
    },
    SetCopy (param) {
      this.copiedTscParam = param
      sessionStorage.tscParam = JSON.stringify(param)
    },
    ClearCopy () {
      this.copiedTscParam = null
      sessionStorage.removeItem('tscParam')
    },

    SaveBodyDomSize (size) {
      this.curBodyWidth = size.width
      this.curBodyHeight = size.height
    },
    SetMenuVisible (isHideMenu) {
      this.hideMenu = isHideMenu
    },
    SetGraphicMode (isSwitchGraphicMode) {
      this.graphicMode = isSwitchGraphicMode
    },
    SetRoadDirection (DIR) {
      this.roadDirection = DIR
    },
    SetChannelDesc (descmap) {
      this.channelDescMap = descmap
    },
    ClearManualPanel () {
      this.tscParam.manualpanel = tscParam.manualpanel
    },
    // SetShowGui (isShowGui) {
      // this.isShowGui = isShowGui
    // },
    SetShowHomePage (curPageType) {
      this.curHomePage = curPageType
    },
    isSetChannelization (isSet) {
      this.isSetChannelization = isSet
    },
    SetRefreshTankuang (isRefreshTankuang) {
      this.isRefreshTankuang = isRefreshTankuang
    },
    SetFromOpenatc (isfromatc) {
      this.isfromatc = isfromatc
    },
    SetOpenatcCurAgentid (curOpenatcAgentid) {
      this.curOpenatcAgentid = curOpenatcAgentid
    },
    SetPlanListOrder (isSort) {
      this.sortPlanList = isSort
    }
  }
})

export const useGlobalStoreWithOut = () => {
  return useGlobalStore(store)
}
