export interface PedestrianSelectorProps {
  imgs: DirectionItem[];
  list: number[];
  index: number;
  disabled: boolean;
  showBottomName: boolean;
  lines: number;
  rows: number;
  showSpan: boolean;
  refresh: boolean;
  showDirIcon: boolean;
  laneIconStyle?: IconStyle;
  pedIconStyle?: IconStyle;
}
export interface PedestrianSelectorEmits {
  (e: 'finsh', payload: { status: number[]; index: number }): void;
}
export interface PedestrianDirection {
  id: number;
  name: string;
  color: string;
}
