import { defineStore } from 'pinia'
import  store  from '../index'

interface GlobalParamModelState {
  GlobalParamModeObject: any,
  devParams: any
}
export const useGlobalParamModelStore = defineStore('globalParamModel', {
  state: (): GlobalParamModelState => {
    return {
    GlobalParamModeObject: Object,
    devParams: Object
    }
  },
  getters: {
    globalParamModel: (state) => state.GlobalParamModeObject
  },
  actions: {
    InitGlobalParamModel (obj) {
      this.GlobalParamModeObject = obj
    },
    SaveDevParams (info) {
      this.devParams = info
    }
  }
})

export const useGlobalParamModelStoreWithOut = () => {
  return useGlobalParamModelStore(store)
}
