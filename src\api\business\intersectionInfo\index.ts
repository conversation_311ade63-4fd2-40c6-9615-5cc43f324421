import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { IntersectionInfoVO, IntersectionInfoForm, IntersectionInfoQuery } from '@/api/business/intersectionInfo/types';

/**
 * 查询路口信息列表
 * @param query
 * @returns {*}
 */

export const listIntersectionInfo = (query?: IntersectionInfoQuery): AxiosPromise<IntersectionInfoVO[]> => {
  return request({
    url: '/business/intersectionInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询路口信息详细
 * @param id
 */
export const getIntersectionInfo = (id: string | number): AxiosPromise<IntersectionInfoVO> => {
  return request({
    url: '/business/intersectionInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增路口信息
 * @param data
 */
export const addIntersectionInfo = (data: IntersectionInfoForm) => {
  return request({
    url: '/business/intersectionInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改路口信息
 * @param data
 */
export const updateIntersectionInfo = (data: IntersectionInfoForm) => {
  return request({
    url: '/business/intersectionInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除路口信息
 * @param id
 */
export const delIntersectionInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/business/intersectionInfo/' + id,
    method: 'delete'
  });
};

/**
 * 查询路口下拉框数据
 */
export const listIntersectionDrop = (): AxiosPromise<IntersectionInfoVO[]> => {
  return request({
    url: '/business/intersectionInfo/optionselect',
    method: 'get'
  });
};
