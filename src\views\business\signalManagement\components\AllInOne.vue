<template>
    <div v-if="isTabsReady">
        <el-tabs :tab-position="tabPosition" class="demo-tabs">
            <el-tab-pane label="相位">
                <Phase></Phase>
            </el-tab-pane>
            <el-tab-pane label="方案">
                <Pattern></Pattern>
            </el-tab-pane>
            <el-tab-pane label="计划">
                <Plan></Plan>
            </el-tab-pane>
            <el-tab-pane label="日计划">
                <Daily></Daily>
            </el-tab-pane>
            <el-tab-pane label="通道">
                <Channel></Channel>
            </el-tab-pane>
            <el-tab-pane label="阶段">
                <Stage></Stage>
            </el-tab-pane>
        </el-tabs>
    </div>
    <div v-else class="loading-placeholder">
        <el-skeleton :rows="3" animated />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import type { TabsInstance } from 'element-plus'
import Channel from './channel/index.vue'
import Daily from './daily/index.vue'
import Pattern from './pattern/index.vue'
import Phase from './phase/index.vue'
import Plan from './plan/index.vue'
import Stage from './stage/index.vue'

const tabPosition = ref<TabsInstance['tabPosition']>('left')
const isTabsReady = ref(false)

// 初始化组件
onMounted(async () => {
    await nextTick()
    isTabsReady.value = true
})
</script>

<style>
.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

.el-tabs--right .el-tabs__content,
.el-tabs--left .el-tabs__content {
    height: 100%;
}

.loading-placeholder {
    padding: 20px;
    min-height: 400px;
}
</style>
