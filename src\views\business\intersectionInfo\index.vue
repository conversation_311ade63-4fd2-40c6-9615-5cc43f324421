<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="路口编码" prop="intersectionCode">
              <el-input v-model="queryParams.intersectionCode" placeholder="请输入路口编码" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="路口名称" prop="intersectionName">
              <el-input v-model="queryParams.intersectionName" placeholder="请输入路口名称" clearable
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="路口类型" prop="intersectionType">
              <el-select v-model="queryParams.intersectionType" placeholder="请选择路口类型" clearable>
                <el-option v-for="dict in utc_intersection_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['business:intersectionInfo:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['business:intersectionInfo:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['business:intersectionInfo:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['business:intersectionInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="intersectionInfoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="路口编码" align="center" prop="intersectionCode" />
        <el-table-column label="路口名称" align="center" prop="intersectionName" />
        <el-table-column label="路口类型" align="center" prop="intersectionType">
          <template #default="scope">
            <dict-tag :options="utc_intersection_type" :value="scope.row.intersectionType" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="路口描述" align="center" prop="intersectionDesc" /> -->
        <el-table-column label="地图经度" align="center" prop="longitude" />
        <el-table-column label="地图纬度" align="center" prop="latitude" />
        <el-table-column label="信号机" align="center" prop="signalName" />
        <!-- <el-table-column label="路口配置" align="center" prop="intersectionConfig" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['business:intersectionInfo:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="渠化" placement="top">
              <el-button link type="primary" @click="handleOpenChannelization(scope.row)"
                v-hasPermi="['business:intersectionInfo:edit']">
                <svg-icon icon-class="channelization" />
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['business:intersectionInfo:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改路口信息对话框 -->
    <el-drawer :title="basicDrawer.title" v-model="basicDrawer.visible" size="50%" append-to-body>
      <el-form ref="intersectionInfoFormRef" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" placeholder="请输入主键" />
        </el-form-item> -->
        <el-form-item label="路口编码" prop="intersectionCode">
          <el-input v-model="form.intersectionCode" placeholder="请输入路口编码" />
        </el-form-item>
        <el-form-item label="路口名称" prop="intersectionName">
          <el-input v-model="form.intersectionName" placeholder="请输入路口名称" />
        </el-form-item>
<!--        <el-form-item label="路口类型" prop="intersectionType">-->
<!--          <el-select v-model="form.intersectionType" placeholder="请选择路口类型" clearable>-->
<!--            <el-option v-for="dict in utc_intersection_type" :key="dict.value" :label="dict.label"-->
<!--              :value="dict.value" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="地图经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入地图经度" />
        </el-form-item>
        <el-form-item label="地图纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入地图纬度" />
        </el-form-item>
        <el-form-item label="信号机" prop="intersectionSignalId">
          <el-select v-model="form.intersectionSignalId" placeholder="请选择信号机厂家">
            <el-option v-for="dict in signalList" :key="dict.id" :label="dict.signalName" :value="dict.id"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="路口配置" prop="intersectionConfig">-->
<!--          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.intersectionConfig" :language="'zh-CN'"-->
<!--            :options="{ mode: 'code' }" @validationError="handleValidationError"-->
<!--            @update:modelValue="handleJsonUpdate" />-->
<!--        </el-form-item>-->
        <el-form-item label="路口描述" prop="intersectionDesc">
          <el-input v-model="form.intersectionDesc" type="textarea" :rows="5" placeholder="请输入内容" />
        </el-form-item>
        <el-row>
          <el-divider content-position="center">地图</el-divider>
          <el-tag type="primary">右键标记位置</el-tag>
          <el-tag type="primary" style="margin-left: 20px;">拖动改变位置</el-tag>
        </el-row>
        <el-row>
          <el-col :span="24">
            <ClickMap :markerClickHandler="handleMarkerClick" :mapWidth="mapWidth + 'px'" :mapHeight="mapHeight + 'px'"
                      :formData="form" :mapCenter="mapCenter"></ClickMap>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="drawer-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>

    <el-drawer :title="channelizationDrawer.title" v-model="channelizationDrawer.visible" size="80%" append-to-body>
      <IntersectionEditor :id="currentId" :config="jsonConfig"  :intersection-type="form.intersectionType" @save="handleChannelizationSave"
        @change="handleChannelizationChange" />
    </el-drawer>
  </div>
</template>

<script setup name="IntersectionInfo" lang="ts">
import { debounce } from 'lodash'

import { listIntersectionInfo, getIntersectionInfo, delIntersectionInfo, addIntersectionInfo, updateIntersectionInfo } from '@/api/business/intersectionInfo';
import { listSignalDrop } from '@/api/business/signalManagement';
import { IntersectionInfoVO, IntersectionInfoQuery, IntersectionInfoForm } from '@/api/business/intersectionInfo/types';
import { IntersectionEditor } from '@/components/IntersectionEditor';
// @ts-ignore
import JsonEditorVue from 'json-editor-vue3'
import { getDefaultConfig,defaultCrossConfig,defaultYConfig,defaultTConfig } from '@/components/IntersectionEditor/src/data';
import { Road } from '@/components/IntersectionEditor/src/types';
import { currentTenantInfo } from '@/api/system/tenant';
import ClickMap from "./clickMap/index.vue";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { utc_intersection_type } = toRefs<any>(proxy?.useDict('utc_intersection_type'));

const currentId = ref< number |string | null>(null);
const jsonConfig = ref(<Road[]>[]);
const intersectionInfoList = ref<IntersectionInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const mapWidth = ref(950);
const mapHeight = ref(750);
const mapCenter = ref([]);

const queryFormRef = ref<ElFormInstance>();
const intersectionInfoFormRef = ref<ElFormInstance>();

const basicDrawer = reactive<DialogOption>({
  visible: false,
  title: ''
});

const channelizationDrawer = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: IntersectionInfoForm = {
  id: undefined,
  intersectionCode: undefined,
  intersectionName: undefined,
  intersectionType: undefined,
  intersectionDesc: undefined,
  longitude: undefined,
  latitude: undefined,
  intersectionSignalId: undefined,
  intersectionConfig: undefined
};
const data = reactive<PageData<IntersectionInfoForm, IntersectionInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    intersectionCode: undefined,
    intersectionName: undefined,
    intersectionType: undefined,
    intersectionSignalId: undefined,
    params: {
    }
  },
  rules: {
    intersectionCode: [
      { required: true, message: "路口编码不能为空", trigger: "blur" }
    ],
    intersectionName: [
      { required: true, message: "路口名称不能为空", trigger: "blur" }
    ],
    longitude: [
      { required: true, message: "地图经度不能为空", trigger: "blur" }
    ],
    latitude: [
      { required: true, message: "地图纬度不能为空", trigger: "blur" }
    ],
    intersectionSignalId: [
      { required: true, message: "信号机不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const signalList = ref([]);
const jsonValid = ref<boolean>(true);

const init = async () => {
  const tenant = await currentTenantInfo();
  console.log("tenant=", tenant);
  const longitude = tenant.data.longitude;
  const latitude = tenant.data.latitude;
  mapCenter.value = [longitude, latitude];
}

/** 查询路口信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listIntersectionInfo(queryParams.value);
  intersectionInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  basicDrawer.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  jsonValid.value = true;
  intersectionInfoFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: IntersectionInfoVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  basicDrawer.visible = true;
  basicDrawer.title = "添加路口信息";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: IntersectionInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getIntersectionInfo(_id);
  Object.assign(form.value, res.data);

  if (res.data.intersectionConfig) {
    const intersectionConfigStr = res.data.intersectionConfig.toString();
    form.value.intersectionConfig = JSON.parse(intersectionConfigStr);
    jsonValid.value = true;
  }

  basicDrawer.visible = true;
  basicDrawer.title = "修改路口信息";
}
const handleOpenChannelization = async (row?: IntersectionInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getIntersectionInfo(_id);
  Object.assign(form.value, res.data);
  currentId.value = _id;
  // console.log("res.data.intersectionConfig=",res.data.intersectionConfig);

  // 处理路口配置数据
  if (res.data.intersectionConfig) {
    try {
      // 确保传递给组件的是对象格式
      if (typeof res.data.intersectionConfig === 'string') {
        jsonConfig.value = JSON.parse(res.data.intersectionConfig);
      } else {
        // 已经是对象，确保是深拷贝避免引用问题
        jsonConfig.value = JSON.parse(JSON.stringify(res.data.intersectionConfig));
      }
    } catch (error) {
      console.error('解析路口配置失败:', error);
      // 使用默认配置
      jsonConfig.value =  getDefaultConfig('cross');
    }
  } else {
    // 如果没有配置，初始化一个默认配置
    jsonConfig.value =   getDefaultConfig('cross');
  }

  // 确保 drawer 完全打开后再初始化组件
  nextTick(() => {
    channelizationDrawer.visible = true;
    channelizationDrawer.title = "渠化路口信息";
  });
}

/** 提交按钮 */
const submitForm = () => {
  intersectionInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await nextTick(); // 确保状态更新完成
      if (!jsonValid.value) {
        proxy?.$modal.msgError("路口配置 JSON 格式错误");
        return;
      }

      buttonLoading.value = true;
      if (form.value.id) {
        await updateIntersectionInfo(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addIntersectionInfo(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      basicDrawer.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: IntersectionInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除路口信息编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delIntersectionInfo(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('business/intersectionInfo/export', {
    ...queryParams.value
  }, `intersectionInfo_${new Date().getTime()}.xlsx`)
}

const setupSingalDrop = async () => {
  const res = await listSignalDrop();
  signalList.value = res.data;
}

const handleValidationError = (errors) => {
  jsonValid.value = false;
}

const handleJsonUpdate = debounce((newJson) => {
  jsonValid.value = true;
}, 300)

/** 处理渠化配置变更 */
const handleChannelizationChange = (configData, id,type) => {
  form.value.intersectionConfig = configData;
  form.value.id = id;
  form.value.intersectionType = type;
}

/** 处理渠化配置保存 */
const handleChannelizationSave = (configData, id,type) => {
  if (!form.value.intersectionConfig) {
    proxy?.$modal.msgError("请先完成路口渠化配置");
    return;
  }

  buttonLoading.value = true;
  form.value.intersectionConfig = configData;
  form.value.id = id;
  form.value.intersectionType = type;
  updateIntersectionInfo(form.value)
    .then(() => {
      proxy?.$modal.msgSuccess("渠化配置保存成功");
      channelizationDrawer.visible = false;
      getList();
    })
    .catch(() => {
      proxy?.$modal.msgError("渠化配置保存失败");
    })
    .finally(() => {
      buttonLoading.value = false;
    });
  console.log('渠化配置已更新:', form.value.intersectionConfig);
}

/** 提交渠化表单 */
const submitChannelizationForm = () => {
  if (!form.value.intersectionConfig) {
    proxy?.$modal.msgError("请先完成路口渠化配置");
    return;
  }

  buttonLoading.value = true;
  updateIntersectionInfo(form.value)
    .then(() => {
      proxy?.$modal.msgSuccess("渠化配置保存成功");
      channelizationDrawer.visible = false;
      getList();
    })
    .catch(() => {
      proxy?.$modal.msgError("渠化配置保存失败");
    })
    .finally(() => {
      buttonLoading.value = false;
    });
}

/** 取消渠化配置 */
const cancelChannelization = () => {
  channelizationDrawer.visible = false;
}


const handleMarkerClick = (value) => {
  console.log("Clicked marker :", value);
  if (value) {
    form.value.longitude = value[0];
    form.value.latitude = value[1];
  }
}

onMounted(() => {
  init();
  getList();
  setupSingalDrop();
});

// 监听 drawer 关闭事件，确保下次打开时能正确初始化
watch(() => channelizationDrawer.visible, (visible) => {
  if (!visible) {
    // drawer 关闭时，确保下次打开能正确初始化
    form.value.intersectionConfig = null;
  }
});
</script>
