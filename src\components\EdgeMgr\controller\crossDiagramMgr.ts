// 该类是路口图的业务逻辑
import EdgeModelBase from '../EdgeModelBase'

export default class crossDiagramMgr extends EdgeModelBase {
  constructor () {
    super('CrossDiagramMgr')
  }

  compare (arr1, arr2, field, iscomparestatus: undefined | string = 'nostatus') {
    // 对比数据算法：相同direction（peddirection），即同方向的情况下，需要综合考虑相位和跟随相位的状态。
    // 以相位数据为基准，如果跟随相位是绿灯，相位是绿闪或者黄灯或红灯，那就取跟随相位绿灯的状态。此状态是相对概念，比对存在优先级。
    // 取值优先级： 绿灯(3) > 绿闪(4) > 黄灯(2) > 红灯(1)
    if (!arr1.length && !arr2.length) return []
    if (arr1.length && !arr2.length) {
      return arr1
    }
    if (!arr1.length && arr2.length) {
      return arr2
    }
    const arr1Ids = arr1.map(ele => ele.id)
    const arr2Ids = arr2.map(ele => ele.id)
    const concatarr: any = []
    for (let i = 0; i < arr1.length; i++) {
      const obj = arr1[i]
      const num = obj.id
      for (let j = 0; j < arr2.length; j++) {
        const aj = arr2[j]
        const n = aj.id
        let laneobj = {}
        if (n === num) {
          // 相同方向，进行相位和跟随相位的比对算法
          if (iscomparestatus === 'nostatus') {
            // nostatus没有状态数据，则返回相位、跟随相位按方向去重后合集
            concatarr.push(obj)
            continue
          }
          laneobj = this.handlePhasePriority(obj, aj, field)
          concatarr.push(laneobj)
          continue
        }
        if (arr1Ids.indexOf(n) === -1) {
          // 有跟随相位，但是没有相位，则取跟随相位的状态数据
          const concatarrIds = concatarr.map(ele => ele.id)
          if (concatarrIds.indexOf(n) === -1) {
            concatarr.push(aj)
          }
        }
        if (arr2Ids.indexOf(num) === -1) {
          // 有相位，但是没有跟随相位，则取相位的状态数据
          const concatarrIds = concatarr.map(ele => ele.id)
          if (concatarrIds.indexOf(num) === -1) {
            concatarr.push(obj)
          }
        }
      }
    }
    return concatarr
  }
  handlePhasePriority (phase, overlap, field) {
    // 根据 绿灯(3) > 绿闪(4) > 黄灯(2) > 红灯(1)的优先级，比对相位、跟随相位，返回优先级高的数据
    const phasetype = phase[field]
    const overlaptype = overlap[field]
    if (phasetype === 3) {
      return phase
    } else if (overlaptype === 3) {
      return overlap
    } else if (phasetype === 4) {
      return phase
    } else if (overlaptype === 4) {
      return overlap
    } else if (phasetype === 2) {
      return phase
    } else if (overlaptype === 2) {
      return overlap
    } else if (phasetype === 1) {
      return phase
    } else if (overlaptype === 1) {
      return overlap
    } else {
      console.log('优先级无法判断')
    }
  }
  getUniqueKey (key) {
    // 生成唯一的key值，防止渲染报错
    const date = Date.now()
    const rund = Math.ceil(Math.random() * 1000)
    const id = key + date + '' + rund
    return id
  }

  compareRepeatDirection (data, field) {
    const ids: number[] = [] // 记录数据id（即direction），用于判断是否有重复的direction
    const uniqueLanePhaseData: any = []
    for (let i = 0; i < data.length; i++) {
      if (ids.indexOf(data[i].id) !== -1) {
        // 比较重复的direction下的状态
        let indexi
        const lastData = uniqueLanePhaseData.filter((ele, index) => {
          if (ele.id === data[i].id) {
            indexi = index
            return true
          }
          return false
        })[0]
        const curData = data[i]
        uniqueLanePhaseData[indexi] = this.compare([lastData], [curData], field)[0]
      } else {
        ids.push(data[i].id)
        uniqueLanePhaseData.push(data[i])
      }
    }
    return uniqueLanePhaseData
  }
}
