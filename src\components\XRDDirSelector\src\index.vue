/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<div :style="{position: 'reletive'}">
  <div :style="{position: 'absolute', left: Data?Data.left:0, top: Data?Data.top:0}">
    <svg
    version="1.1"
    id="图层_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 60 60"
    :width="Width"
    :height="Height"
    style="enable-background:new 0 0 60 60;"
    xml:space="preserve">
    <g>
    <g id="南">
    <path id="南掉头"
      :stroke-width="PathWidth"
      :fill="status[15].color"
      :class="status[15].isshow ? '' : 'invisible'"
      class="st0" d="M20.6,23.8c-2.2,0-4,1.8-4,4v0.4h-2.7l3.8,5.1l3.8-5.1h-2.9v-0.4c0-1.2,1-2.1,2.1-2.1
      c1.2,0,2.1,1,2.1,2.1v5.5h1.9v-5.5C24.5,25.6,22.8,23.8,20.6,23.8z"/>
      <polygon id="南直行"
      :stroke-width="PathWidth"
      :fill="status[12].color"
      :class="status[12].isshow ? '' : 'invisible'"
      class="st0" points="27.5,17.8 23.7,12.6 20,17.8 22.8,17.8 22.8,33.3 24.7,33.3 24.7,17.8"/>
    <path id="南左转"
      :stroke-width="PathWidth"
      :fill="status[13].color"
      :class="status[13].isshow ? '' : 'invisible'"
      class="st0" d="M24.7,33.3c0,0,0-5.5,0-5.5c0-2.2-1.2-4.5-3.1-5.7c-0.2-0.1-0.5-0.3-0.7-0.4
      c-1.2-0.5-2.5-0.7-3.7-0.9c0,0,0-0.2,0-0.2l0.2-2.8L12,21.2l4.8,4.2c0,0,0.2-2.6,0.2-2.6c0,0,1.3,0.2,1.4,0.2
      c1.7,0.3,3,1.1,3.8,2.7c0.6,1.2,0.5,2.4,0.5,3.7c0,1,0,2,0,3c0,0.3,0,0.6,0,1H24.7z"/>
    <path id="南右转"
      :stroke-width="PathWidth"
      :fill="status[14].color"
      :class="status[14].isshow ? '' : 'invisible'"
      class="st0" d="M33.4,21.4L28,18.1l0.2,2.8l0,0.2l0,0.1c-0.6,0.1-1.1,0.3-1.7,0.5c-0.2,0.1-0.5,0.2-0.7,0.4
      c-1.9,1.2-3.1,3.4-3.1,5.7c0,0,0,5.5,0,5.5h1.9c0-0.3,0-0.6,0-1c0-1,0-2,0-3c0-1.3-0.1-2.5,0.5-3.7c0.7-1.4,1.8-2.2,3.2-2.5
      c0,0.5,0.2,2.5,0.2,2.5L33.4,21.4z"/>
    </g>
    <g id="北">
    <path id="北掉头"
      :stroke-width="PathWidth"
      :fill="status[11].color"
      :class="status[11].isshow ? '' : 'invisible'"
      class="st0" d="M13.4,10.2c2.2,0,4-1.8,4-4V5.8h2.7l-3.8-5.1l-3.8,5.1h2.9v0.4c0,1.2-1,2.1-2.1,2.1
      s-2.1-1-2.1-2.1V0.7H9.3v5.5C9.5,8.4,11.2,10.2,13.4,10.2z"/>
    <polygon
      id="北直行_1_"
      :stroke-width="PathWidth"
      :fill="status[8].color"
      :class="status[8].isshow ? '' : 'invisible'"
      class="st0" points="6.5,16.1 10.3,21.3 14,16.1 11.2,16.1 11.2,0.6 9.3,0.6 9.3,16.1"/>
    <path id="北左转"
      :stroke-width="PathWidth"
      :fill="status[9].color"
      :class="status[9].isshow ? '' : 'invisible'"
      class="st0" d="M9.3,0.7c0,0,0,5.5,0,5.5c0,2.2,1.2,4.5,3.1,5.7c0.2,0.1,0.5,0.3,0.7,0.4
      c1.2,0.5,2.5,0.7,3.7,0.9c0,0,0,0.2,0,0.2l-0.2,2.8l5.4-3.3l-4.8-4.2c0,0-0.2,2.6-0.2,2.6c0,0-1.3-0.2-1.4-0.2
      c-1.7-0.3-3-1.1-3.8-2.7c-0.6-1.2-0.5-2.4-0.5-3.7c0-1,0-2,0-3c0-0.3,0-0.6,0-1H9.3z"/>
    <path id="北右转"
      :stroke-width="PathWidth"
      :fill="status[10].color"
      :class="status[10].isshow ? '' : 'invisible'"
      class="st0" d="M0.6,12.6L6,15.9l-0.2-2.8l0-0.2l0-0.1c0.6-0.1,1.1-0.3,1.7-0.5c0.2-0.1,0.5-0.2,0.7-0.4
      c1.9-1.2,3.1-3.4,3.1-5.7c0,0,0-5.5,0-5.5H9.3c0,0.3,0,0.6,0,1c0,1,0,2,0,3c0,1.3,0.1,2.5-0.5,3.7C8.1,9.7,7,10.5,5.6,10.9
      c0-0.5-0.2-2.5-0.2-2.5L0.6,12.6z"/>
    </g>
    <g id="西">
    <path id="西掉头"
      :stroke-width="PathWidth"
      :fill="status[7].color"
      :class="status[7].isshow ? '' : 'invisible'"
      class="st0" d="M10.2,20.6c0-2.2-1.8-4-4-4H5.8v-2.7l-5.1,3.8l5.1,3.8v-2.9h0.4c1.2,0,2.1,1,2.1,2.1
      c0,1.2-1,2.1-2.1,2.1H0.7v1.9h5.5C8.4,24.5,10.2,22.8,10.2,20.6z"/>
      <polygon id="西直行"
      :stroke-width="PathWidth"
      :fill="status[4].color"
      :class="status[4].isshow ? '' : 'invisible'"
      class="st0"
      points="16.3,27.5 21.4,23.7 16.3,20 16.3,22.8 0.8,22.8 0.8,24.7 16.3,24.7"/>
    <path id="西左转"
      :stroke-width="PathWidth"
      :fill="status[5].color"
      :class="status[5].isshow ? '' : 'invisible'"
      class="st0" d="M0.7,24.7c0,0,5.5,0,5.5,0c2.2,0,4.5-1.2,5.7-3.1c0.1-0.2,0.3-0.5,0.4-0.7
      c0.5-1.2,0.7-2.5,0.9-3.7c0,0,0.2,0,0.2,0l2.8,0.2L12.8,12l-4.2,4.8c0,0,2.6,0.2,2.6,0.2c0,0-0.2,1.3-0.2,1.4
      c-0.3,1.7-1.1,3-2.7,3.8c-1.2,0.6-2.4,0.5-3.7,0.5c-1,0-2,0-3,0c-0.3,0-0.6,0-1,0V24.7z"/>
    <path id="西右转"
      :stroke-width="PathWidth"
      :fill="status[6].color"
      :class="status[6].isshow ? '' : 'invisible'"
      class="st0" d="M12.6,33.4l3.3-5.4l-2.8,0.2l-0.2,0l-0.1,0c-0.1-0.6-0.3-1.1-0.5-1.7
      c-0.1-0.2-0.2-0.5-0.4-0.7c-1.2-1.9-3.4-3.1-5.7-3.1c0,0-5.5,0-5.5,0v1.9c0.3,0,0.6,0,1,0c1,0,2,0,3,0c1.3,0,2.5-0.1,3.7,0.5
      c1.4,0.7,2.2,1.8,2.5,3.2c-0.5,0-2.5,0.2-2.5,0.2L12.6,33.4z"/>
    </g>
    <g id="东">
    <path id="东掉头"
      :stroke-width="PathWidth"
      :fill="status[3].color"
      :class="status[3].isshow ? '' : 'invisible'"
      class="st0" d="M23.8,13.4c0,2.2,1.8,4,4,4h0.4v2.7l5.1-3.8l-5.1-3.8v2.9h-0.4c-1.2,0-2.1-1-2.1-2.1
      s1-2.1,2.1-2.1h5.5V9.3h-5.5C25.6,9.5,23.8,11.2,23.8,13.4z"/>
      <polygon
      :stroke-width="PathWidth"
      :fill="status[0].color"
      :class="status[0].isshow ? '' : 'invisible'"
      id="东直行"
      class="st0"
      points="17.9,6.5 12.8,10.3 17.9,14 17.9,11.2 33.4,11.2 33.4,9.3 17.9,9.3"/>
    <path id="东左转"
      :stroke-width="PathWidth"
      :fill="status[1].color"
      :class="status[1].isshow ? '' : 'invisible'"
      class="st0" d="M33.3,9.3c0,0-5.5,0-5.5,0c-2.2,0-4.5,1.2-5.7,3.1c-0.1,0.2-0.3,0.5-0.4,0.7
      c-0.5,1.2-0.7,2.5-0.9,3.7c0,0-0.2,0-0.2,0l-2.8-0.2l3.3,5.4l4.2-4.8c0,0-2.6-0.2-2.6-0.2c0,0,0.2-1.3,0.2-1.4
      c0.3-1.7,1.1-3,2.7-3.8c1.2-0.6,2.4-0.5,3.7-0.5c1,0,2,0,3,0c0.3,0,0.6,0,1,0V9.3z"/>
    <path id="东右转"
      :stroke-width="PathWidth"
      :fill="status[2].color"
      :class="status[2].isshow ? '' : 'invisible'"
      class="st0" d="M21.4,0.6L18.1,6l2.8-0.2l0.2,0l0.1,0c0.1,0.6,0.3,1.1,0.5,1.7c0.1,0.2,0.2,0.5,0.4,0.7
      c1.2,1.9,3.4,3.1,5.7,3.1c0,0,5.5,0,5.5,0V9.3c-0.3,0-0.6,0-1,0c-1,0-2,0-3,0c-1.3,0-2.5,0.1-3.7-0.5c-1.4-0.7-2.2-1.8-2.5-3.2
      c0.5,0,2.5-0.2,2.5-0.2L21.4,0.6z"/>
    </g>
    </g>
  <g>
  <g id="西南">
    <path id="西南掉头"
      :stroke-width="PathWidth"
      :fill="status[23].color"
      :class="status[23].isshow ? '' : 'invisible'"
      class="st0" d="M14.7,24.4c-1.6-1.6-4.1-1.5-5.6,0l-0.3,0.3l-1.9-1.9L6,29l6.3-1l-2-2l0.3-0.3
    c0.8-0.8,2.2-0.8,3,0c0.8,0.8,0.8,2.2,0,3l-3.9,3.9l1.4,1.4l3.9-3.9C16.2,28.4,16.2,25.9,14.7,24.4z"/>
    <polygon
      id="西南直行"
      class="st0"
      :stroke-width="PathWidth"
      :fill="status[20].color"
      :class="status[20].isshow ? '' : 'invisible'"
    points="23.8,25 24.8,18.8 18.4,19.7 20.4,21.7 9.6,32.5 11,33.9 21.9,23"/>
    <path id="西南左转"
      :stroke-width="PathWidth"
      :fill="status[21].color"
      :class="status[21].isshow ? '' : 'invisible'"
      class="st0" d="M10.9,34c0,0,3.9-3.9,3.9-3.9c1.6-1.6,2.3-4,1.8-6.2c-0.1-0.3-0.1-0.5-0.2-0.7
      c-0.5-1.2-1.3-2.2-2-3.3c0,0,0.1-0.1,0.1-0.1l2.1-1.8l-6.2-1.5l0.4,6.3c0,0,2-1.7,2-1.7c0,0,0.8,1.1,0.8,1.2
      c1,1.4,1.3,2.9,0.8,4.6c-0.4,1.3-1.3,2.1-2.2,3c-0.7,0.7-1.4,1.4-2.1,2.1c-0.2,0.2-0.4,0.4-0.7,0.7L10.9,34z"/>
    <path id="西南右转"
      :stroke-width="PathWidth"
      :fill="status[22].color"
      :class="status[22].isshow ? '' : 'invisible'"
      class="st0" d="M25.4,31.7L24,25.6l-1.8,2.1L22,27.8L22,27.9c-0.5-0.3-1-0.6-1.5-0.8
      c-0.2-0.1-0.5-0.2-0.7-0.2c-2.2-0.5-4.6,0.2-6.2,1.8c0,0-3.9,3.9-3.9,3.9l1.4,1.4c0.2-0.2,0.4-0.4,0.7-0.7
      c0.7-0.7,1.4-1.4,2.1-2.1c0.9-0.9,1.7-1.8,3-2.2c1.4-0.5,2.8-0.3,4,0.5c-0.3,0.4-1.6,1.9-1.6,1.9L25.4,31.7z"/>
    </g>
    <g id="东北">
    <path id="东北掉头"
      :stroke-width="PathWidth"
      :fill="status[27].color"
      :class="status[27].isshow ? '' : 'invisible'"
      class="st0" d="M19.3,9.6c1.6,1.6,4.1,1.5,5.6,0l0.3-0.3l1.9,1.9L28,5l-6.3,1l2,2l-0.3,0.3
      c-0.8,0.8-2.2,0.8-3,0c-0.8-0.8-0.8-2.2,0-3l3.9-3.9L23.1,0l-3.9,3.9C17.8,5.6,17.8,8.1,19.3,9.6z"/>
      <polygon
      :stroke-width="PathWidth"
      :fill="status[24].color"
      :class="status[24].isshow ? '' : 'invisible'"
      id="东北直行"
      class="st0"
      points="10.2,9 9.2,15.2 15.4,14.3 13.4,12.3 24.4,1.4 23.1,0 12,11"/>
    <path id="东北左转"
      :stroke-width="PathWidth"
      :fill="status[25].color"
      :class="status[25].isshow ? '' : 'invisible'"
      class="st0" d="M23.1,0c0,0-3.9,3.9-3.9,3.9c-1.6,1.6-2.3,4-1.8,6.2c0.1,0.3,0.1,0.5,0.2,0.7
      c0.5,1.2,1.3,2.2,2,3.3c0,0-0.1,0.1-0.1,0.1l-2.1,1.8l6.2,1.5l-0.4-6.3c0,0-2,1.7-2,1.7c0,0-0.8-1.1-0.8-1.2
      c-1-1.4-1.3-2.9-0.8-4.6c0.4-1.3,1.3-2.1,2.2-3c0.7-0.7,1.4-1.4,2.1-2.1c0.2-0.2,0.4-0.4,0.7-0.7L23.1,0z"/>
    <path id="东北右转"
      :stroke-width="PathWidth"
      :fill="status[26].color"
      :class="status[26].isshow ? '' : 'invisible'"
      class="st0" d="M8.6,2.3L10,8.4l1.8-2.1L12,6.2L12,6.1c0.5,0.3,1,0.6,1.5,0.8c0.2,0.1,0.5,0.2,0.7,0.2
      c2.2,0.5,4.6-0.2,6.2-1.8c0,0,3.9-3.9,3.9-3.9L23.1,0c-0.2,0.2-0.4,0.4-0.7,0.7c-0.7,0.7-1.4,1.4-2.1,2.1c-0.9,0.9-1.7,1.8-3,2.2
      c-1.4,0.5-2.8,0.3-4-0.5c0.3-0.4,1.6-1.9,1.6-1.9L8.6,2.3z"/>
    </g>
    <g id="西北">
    <path id="西北掉头"
      :stroke-width="PathWidth"
      :fill="status[31].color"
      :class="status[31].isshow ? '' : 'invisible'"
      class="st0" d="M9.6,14.7c1.6-1.6,1.5-4.1,0-5.6L9.4,8.8l1.9-1.9L5,6l1,6.3l2-2l0.3,0.3
    c0.8,0.8,0.8,2.2,0,3c-0.8,0.8-2.2,0.8-3,0L1.4,9.6L0,10.9l3.9,3.9C5.6,16.2,8.1,16.2,9.6,14.7z"/>
    <polygon
      :stroke-width="PathWidth"
      :fill="status[28].color"
      :class="status[28].isshow ? '' : 'invisible'"
      id="西北直行"
      class="st0"
      points="8.9,23.8 15.1,24.8 14.3,18.5 12.3,20.5 1.4,9.5 0,10.9 10.9,21.9"/>
    <path id="西北左转"
      :stroke-width="PathWidth"
      :fill="status[21].color"
      :class="status[21].isshow ? '' : 'invisible'"
      class="st0" d="M0,10.9c0,0,3.9,3.9,3.9,3.9c1.6,1.6,4,2.3,6.2,1.8c0.3-0.1,0.5-0.1,0.7-0.2
    c1.2-0.5,2.2-1.3,3.3-2c0,0,0.1,0.1,0.1,0.1l1.8,2.1l1.5-6.2l-6.3,0.4c0,0,1.7,2,1.7,2c0,0-1.1,0.8-1.2,0.8
    c-1.4,1-2.9,1.3-4.6,0.8c-1.3-0.4-2.1-1.3-3-2.2c-0.7-0.7-1.4-1.4-2.1-2.1C1.8,10,1.6,9.8,1.4,9.6L0,10.9z"/>
    <path id="西北右转"
      :stroke-width="PathWidth"
      :fill="status[30].color"
      :class="status[30].isshow ? '' : 'invisible'"
      class="st0" d="M2.3,25.4L8.4,24l-2.1-1.8L6.2,22L6.1,22c0.3-0.5,0.6-1,0.8-1.5c0.1-0.2,0.2-0.5,0.2-0.7
      c0.5-2.2-0.2-4.6-1.8-6.2c0,0-3.9-3.9-3.9-3.9L0,10.9c0.2,0.2,0.4,0.4,0.7,0.7c0.7,0.7,1.4,1.4,2.1,2.1c0.9,0.9,1.8,1.7,2.2,3
      c0.5,1.4,0.3,2.8-0.5,4c-0.4-0.3-1.9-1.6-1.9-1.6L2.3,25.4z"/>
      </g>
    <g id="东南">
    <path id="东南掉头"
      :stroke-width="PathWidth"
      :fill="status[19].color"
      :class="status[19].isshow ? '' : 'invisible'"
      class="st0" d="M24.4,19.3c-1.6,1.6-1.5,4.1,0,5.6l0.3,0.3l-1.9,1.9l6.3,1l-1-6.3l-2,2l-0.3-0.3
    c-0.8-0.8-0.8-2.2,0-3c0.8-0.8,2.2-0.8,3,0l3.9,3.9l1.4-1.4l-3.9-3.9C28.4,17.8,25.9,17.8,24.4,19.3z"/>
    <polygon
      id="东南直行"
      :stroke-width="PathWidth"
      :fill="status[16].color"
      :class="status[16].isshow ? '' : 'invisible'"
      class="st0"
      points="24.9,10.2 18.8,9.2 19.7,15.5 21.7,13.5 32.6,24.4 33.9,23.1 22.9,12.1"/>
    <path id="东南左转"
      :stroke-width="PathWidth"
      :fill="status[17].color"
      :class="status[17].isshow ? '' : 'invisible'"
      class="st0" d="M34,23.1c0,0-3.9-3.9-3.9-3.9c-1.6-1.6-4-2.3-6.2-1.8c-0.3,0.1-0.5,0.1-0.7,0.2
    c-1.2,0.5-2.2,1.3-3.3,2c0,0-0.1-0.1-0.1-0.1l-1.8-2.1l-1.5,6.2l6.3-0.4c0,0-1.7-2-1.7-2c0,0,1.1-0.8,1.2-0.8
    c1.4-1,2.9-1.3,4.6-0.8c1.3,0.4,2.1,1.3,3,2.2c0.7,0.7,1.4,1.4,2.1,2.1c0.2,0.2,0.4,0.4,0.7,0.7L34,23.1z"/>
    <path id="东南右转"
      :stroke-width="PathWidth"
      :fill="status[18].color"
      :class="status[18].isshow ? '' : 'invisible'"
      class="st0" d="M31.7,8.6L25.6,10l2.1,1.8l0.1,0.1l0.1,0.1c-0.3,0.5-0.6,1-0.8,1.5
      c-0.1,0.2-0.2,0.5-0.2,0.7c-0.5,2.2,0.2,4.6,1.8,6.2c0,0,3.9,3.9,3.9,3.9l1.4-1.4c-0.2-0.2-0.4-0.4-0.7-0.7
      c-0.7-0.7-1.4-1.4-2.1-2.1c-0.9-0.9-1.8-1.7-2.2-3c-0.5-1.4-0.3-2.8,0.5-4c0.4,0.3,1.9,1.6,1.9,1.6L31.7,8.6z"/>
      </g>
      </g>
  </svg>
  </div>
  <div :style="{position: 'absolute', left: Datas?Datas.left:0, top: Datas?Datas.top:0}">
  <svg
    version="1.1"
    id="图层_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 50 50"
    style="enable-background:new 0 0 50 50;"
    :width="Widths"
    :height="Heights"
    xml:space="preserve">
  <!-- <style type="text/css">
  .st0{fill:#CBD3DB;}
  </style> -->
  <g>
  <g id="二次过街-西">
    <g id="西上" :fill="peoplestatus[9].color" :class="peoplestatus[9].isshow? '' : 'invisible'">
      <rect y="0" class="st0" width="4.4" height="2.6"/>
      <rect y="5.2" class="st0" width="4.4" height="2.6"/>
      <rect y="10.5" class="st0" width="4.4" height="2.6"/>
    </g>
    <g id="西下" :fill="peoplestatus[8].color" :class="peoplestatus[8].isshow? '' : 'invisible'">
      <rect y="20.9" class="st0" width="4.4" height="2.6"/>
      <rect y="26.2" class="st0" width="4.4" height="2.6"/>
      <rect y="31.4" class="st0" width="4.4" height="2.6"/>
    </g>
  </g>
  <g id="二次过街-东">
    <g id="东上" :fill="peoplestatus[10].color" :class="peoplestatus[10].isshow? '' : 'invisible'">
      <rect x="29.6" y="0" class="st0" width="4.4" height="2.6"/>
      <rect x="29.6" y="5.2" class="st0" width="4.4" height="2.6"/>
      <rect x="29.6" y="10.5" class="st0" width="4.4" height="2.6"/>
    </g>
    <g id="东下" :fill="peoplestatus[11].color" :class="peoplestatus[11].isshow? '' : 'invisible'">
      <rect x="29.6" y="20.9" class="st0" width="4.4" height="2.6"/>
      <rect x="29.6" y="26.2" class="st0" width="4.4" height="2.6"/>
      <rect x="29.6" y="31.4" class="st0" width="4.4" height="2.6"/>
    </g>
  </g>
  <g id="二次过街-北">
    <g id="北右" :fill="peoplestatus[14].color" :class="peoplestatus[14].isshow? '' : 'invisible'">
      <rect x="31.4" class="st0" width="2.6" height="4.4"/>
      <rect x="26.2" class="st0" width="2.6" height="4.4"/>
      <rect x="20.9" class="st0" width="2.6" height="4.4"/>
    </g>
    <g id="北左" :fill="peoplestatus[15].color" :class="peoplestatus[15].isshow? '' : 'invisible'">
      <rect x="10.5" class="st0" width="2.6" height="4.4"/>
      <rect x="5.2" class="st0" width="2.6" height="4.4"/>
      <rect x="0" class="st0" width="2.6" height="4.4"/>
    </g>
  </g>
  <g id="二次过街-南">
    <g id="南右" :fill="peoplestatus[12].color" :class="peoplestatus[12].isshow? '' : 'invisible'">
      <rect x="31.4" y="29.6" class="st0" width="2.6" height="4.4"/>
      <rect x="26.2" y="29.6" class="st0" width="2.6" height="4.4"/>
      <rect x="20.9" y="29.6" class="st0" width="2.6" height="4.4"/>
    </g>
    <g id="南左" :fill="peoplestatus[13].color" :class="peoplestatus[13].isshow? '' : 'invisible'">
      <rect x="10.5" y="29.6" class="st0" width="2.6" height="4.4"/>
      <rect x="5.2" y="29.6" class="st0" width="2.6" height="4.4"/>
      <rect x="0" y="29.6" class="st0" width="2.6" height="4.4"/>
    </g>
  </g>
  <g id="西行人_1_" :fill="peoplestatus[7].color" :class="peoplestatus[7].isshow? '' : 'invisible'">
    <rect y="0" class="st0" width="4.4" height="2.6"/>
    <rect y="5.2" class="st0" width="4.4" height="2.6"/>
    <rect y="10.5" class="st0" width="4.4" height="2.6"/>
    <rect y="15.7" class="st0" width="4.4" height="2.6"/>
    <rect y="20.9" class="st0" width="4.4" height="2.6"/>
    <rect y="26.2" class="st0" width="4.4" height="2.6"/>
    <rect y="31.4" class="st0" width="4.4" height="2.6"/>
  </g>
  <g id="东行人_1_" :fill="peoplestatus[6].color" :class="peoplestatus[6].isshow? '' : 'invisible'">
    <rect x="29.6" y="0" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="5.2" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="10.5" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="15.7" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="20.9" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="26.2" class="st0" width="4.4" height="2.6"/>
    <rect x="29.6" y="31.4" class="st0" width="4.4" height="2.6"/>
  </g>
  <g id="北行人_1_" :fill="peoplestatus[4].color" :class="peoplestatus[4].isshow? '' : 'invisible'">
    <rect x="31.4" class="st0" width="2.6" height="4.4"/>
    <rect x="26.2" class="st0" width="2.6" height="4.4"/>
    <rect x="20.9" class="st0" width="2.6" height="4.4"/>
    <rect x="15.7" class="st0" width="2.6" height="4.4"/>
    <rect x="10.5" class="st0" width="2.6" height="4.4"/>
    <rect x="5.2" class="st0" width="2.6" height="4.4"/>
    <rect x="0" class="st0" width="2.6" height="4.4"/>
  </g>
  <g id="斜向行人1" :fill="peoplestatus[2].color"  :class="peoplestatus[2].isshow? '' : 'invisible'">
      <rect x="25.9" y="26.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -11.6378 28.0961)" class="st0" width="4.4" height="2.6"/>
      <rect x="22.2" y="23.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -10.1057 24.3974)" class="st0" width="4.4" height="2.6"/>
      <rect x="18.5" y="19.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -8.5737 20.6987)" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="15.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -7.0416 17)" class="st0" width="4.4" height="2.6"/>
    <rect x="11.1" y="12" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -5.5096 13.3013)" class="st0" width="4.4" height="2.6"/>
    <rect x="7.4" y="8.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -3.9775 9.6026)" class="st0" width="4.4" height="2.6"/>
    <rect x="3.7" y="4.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -2.4455 5.9039)" class="st0" width="4.4" height="2.6"/>
  </g>
  <g id="斜向行人2" :fill="peoplestatus[3].color" :class="peoplestatus[3].isshow? '' : 'invisible'">
      <rect x="4.6" y="25.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -18.1378 12.4038)" class="st0" width="2.6" height="4.4"/>
      <rect x="8.3" y="22.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -14.4391 13.9359)" class="st0" width="2.6" height="4.4"/>
    <rect x="12" y="18.5" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -10.7403 15.4679)" class="st0" width="2.6" height="4.4"/>
    <rect x="15.7" y="14.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -7.0416 17)" class="st0" width="2.6" height="4.4"/>
    <rect x="19.4" y="11.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -3.3429 18.532)" class="st0" width="2.6" height="4.4"/>
    <rect x="23.1" y="7.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 0.3558 20.0641)" class="st0" width="2.6" height="4.4"/>
    <rect x="26.8" y="3.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 4.0545 21.5962)" class="st0" width="2.6" height="4.4"/>
  </g>
  <g id="路段行人过街-南北" :fill="peoplestatus[0].color" :class="peoplestatus[0].isshow? '' : 'invisible'">
    <rect x="14.8" y="31.4" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="26.2" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="20.9" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="15.7" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="10.5" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="5.2" class="st0" width="4.4" height="2.6"/>
    <rect x="14.8" y="0" class="st0" width="4.4" height="2.6"/>
  </g>
  <g id="路段行人过街-东西" :fill="peoplestatus[1].color" :class="peoplestatus[1].isshow? '' : 'invisible'">
    <rect x="0" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="5.2" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="10.5" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="15.7" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="20.9" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="26.2" y="14.8" class="st0" width="2.6" height="4.4"/>
    <rect x="31.4" y="14.8" class="st0" width="2.6" height="4.4"/>
  </g>
  <g id="南行人_1_" :fill="peoplestatus[5].color" :class="peoplestatus[5].isshow? '' : 'invisible'">
    <rect x="31.4" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="26.2" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="20.9" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="15.7" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="10.5" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="5.2" y="29.6" class="st0" width="2.6" height="4.4"/>
    <rect x="0" y="29.6" class="st0" width="2.6" height="4.4"/>
  </g>
  </g>
  <g>
  <g id="二次过街-西北">
    <g id="西北上" :fill="peoplestatus[26].color" :class="peoplestatus[26].isshow? '' : 'invisible'">
        <rect x="16.3" y="-6.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 8.3878 11.1346)" class="st0" width="2.6" height="4.4"/>
      <rect x="12.6" y="-3.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 4.6891 9.6026)" class="st0" width="2.6" height="4.4"/>
      <rect x="8.9" y="0.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 0.9904 8.0705)" class="st0" width="2.6" height="4.4"/>
    </g>
    <g id="西北下" :fill="peoplestatus[27].color" :class="peoplestatus[27].isshow? '' : 'invisible'">
      <rect x="1.5" y="8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -6.407 5.0064)" class="st0" width="2.6" height="4.4"/>
      <rect x="-2.2" y="11.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -10.1057 3.4743)" class="st0" width="2.6" height="4.4"/>
      <rect x="-5.9" y="15.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -13.8044 1.9423)" class="st0" width="2.6" height="4.4"/>
    </g>
  </g>
  <g id="二次过街-东南">
    <g id="东南上" :fill="peoplestatus[20].color" :class="peoplestatus[20].isshow? '' : 'invisible'">
        <rect x="37.3" y="14.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -0.2788 32.0577)" class="st0" width="2.6" height="4.4"/>
        <rect x="33.6" y="17.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -3.9775 30.5257)" class="st0" width="2.6" height="4.4"/>
        <rect x="29.9" y="21.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -7.6762 28.9936)" class="st0" width="2.6" height="4.4"/>
    </g>
    <g id="东南下" :fill="peoplestatus[21].color" :class="peoplestatus[21].isshow? '' : 'invisible'">
        <rect x="22.5" y="29" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -15.0736 25.9295)" class="st0" width="2.6" height="4.4"/>
        <rect x="18.8" y="32.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -18.7724 24.3974)" class="st0" width="2.6" height="4.4"/>
        <rect x="15.1" y="36.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -22.4711 22.8654)" class="st0" width="2.6" height="4.4"/>
    </g>
  </g>
  <g id="二次过街-东北">
    <g id="东北下" :fill="peoplestatus[25].color" :class="peoplestatus[25].isshow? '' : 'invisible'">
      <rect x="36.4" y="16.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -1.1763 32.4295)" class="st0" width="4.4" height="2.6"/>
      <rect x="32.7" y="12.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 0.3558 28.7307)" class="st0" width="4.4" height="2.6"/>
      <rect x="29" y="8.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 1.8879 25.032)" class="st0" width="4.4" height="2.6"/>
    </g>
    <g id="东北上" :fill="peoplestatus[24].color" :class="peoplestatus[24].isshow? '' : 'invisible'">
      <rect x="21.6" y="1.5" transform="matrix(0.7071 -0.7071 0.7071 0.7071 4.952 17.6346)" class="st0" width="4.4" height="2.6"/>
      <rect x="17.9" y="-2.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 6.484 13.9359)" class="st0" width="4.4" height="2.6"/>
      <rect x="14.2" y="-5.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 8.0161 10.2372)" class="st0" width="4.4" height="2.6"/>
    </g>
  </g>
  <g id="二次过街-西南">
    <g id="西南下" :fill="peoplestatus[23].color" :class="peoplestatus[23].isshow? '' : 'invisible'">
        <rect x="15.4" y="37.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -22.0994 23.7628)" class="st0" width="4.4" height="2.6"/>
        <rect x="11.7" y="33.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -20.5673 20.0641)" class="st0" width="4.4" height="2.6"/>
      <rect x="8" y="29.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -19.0352 16.3654)" class="st0" width="4.4" height="2.6"/>
    </g>
    <g id="西南上" :fill="peoplestatus[22].color" :class="peoplestatus[22].isshow? '' : 'invisible'">
      <rect x="0.6" y="22.5" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -15.9711 8.968)" class="st0" width="4.4" height="2.6"/>
        <rect x="-3.1" y="18.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -14.4391 5.2693)" class="st0" width="4.4" height="2.6"/>
        <rect x="-6.8" y="15.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -12.907 1.5706)" class="st0" width="4.4" height="2.6"/>
    </g>
  </g>
  <g id="西北行人" :fill="peoplestatus[19].color" :class="peoplestatus[19].isshow? '' : 'invisible'">
    <rect x="16.3" y="-6.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 8.3878 11.1346)" class="st0" width="2.6" height="4.4"/>
    <rect x="12.6" y="-3.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 4.6891 9.6026)" class="st0" width="2.6" height="4.4"/>
    <rect x="8.9" y="0.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 0.9904 8.0705)" class="st0" width="2.6" height="4.4"/>
    <rect x="5.2" y="4.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -2.7083 6.5385)" class="st0" width="2.6" height="4.4"/>
    <rect x="1.5" y="8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -6.407 5.0064)" class="st0" width="2.6" height="4.4"/>
      <rect x="-2.2" y="11.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -10.1057 3.4743)" class="st0" width="2.6" height="4.4"/>
      <rect x="-5.9" y="15.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -13.8044 1.9423)" class="st0" width="2.6" height="4.4"/>
  </g>
  <g id="东南行人" :fill="peoplestatus[16].color" :class="peoplestatus[16].isshow? '' : 'invisible'">
      <rect x="37.3" y="14.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -0.2788 32.0577)" class="st0" width="2.6" height="4.4"/>
      <rect x="33.6" y="17.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -3.9775 30.5257)" class="st0" width="2.6" height="4.4"/>
      <rect x="29.9" y="21.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -7.6762 28.9936)" class="st0" width="2.6" height="4.4"/>
      <rect x="26.2" y="25.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -11.3749 27.4615)" class="st0" width="2.6" height="4.4"/>
    <rect x="22.5" y="29" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -15.0736 25.9295)" class="st0" width="2.6" height="4.4"/>
      <rect x="18.8" y="32.7" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -18.7724 24.3974)" class="st0" width="2.6" height="4.4"/>
      <rect x="15.1" y="36.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -22.4711 22.8654)" class="st0" width="2.6" height="4.4"/>
  </g>
  <g id="东北行人" :fill="peoplestatus[18].color" :class="peoplestatus[18].isshow? '' : 'invisible'">
    <rect x="36.4" y="16.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -1.1763 32.4295)" class="st0" width="4.4" height="2.6"/>
    <rect x="32.7" y="12.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 0.3558 28.7307)" class="st0" width="4.4" height="2.6"/>
    <rect x="29" y="8.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 1.8879 25.032)" class="st0" width="4.4" height="2.6"/>
    <rect x="25.3" y="5.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 3.4199 21.3333)" class="st0" width="4.4" height="2.6"/>
    <rect x="21.6" y="1.5" transform="matrix(0.7071 -0.7071 0.7071 0.7071 4.952 17.6346)" class="st0" width="4.4" height="2.6"/>
    <rect x="17.9" y="-2.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 6.484 13.9359)" class="st0" width="4.4" height="2.6"/>
    <rect x="14.2" y="-5.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 8.0161 10.2372)" class="st0" width="4.4" height="2.6"/>
  </g>
  <g id="西南行人" :fill="peoplestatus[17].color" :class="peoplestatus[17].isshow? '' : 'invisible'">
      <rect x="15.4" y="37.3" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -22.0994 23.7628)" class="st0" width="4.4" height="2.6"/>
      <rect x="11.7" y="33.6" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -20.5673 20.0641)" class="st0" width="4.4" height="2.6"/>
    <rect x="8" y="29.9" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -19.0352 16.3654)" class="st0" width="4.4" height="2.6"/>
      <rect x="4.3" y="26.2" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -17.5032 12.6667)" class="st0" width="4.4" height="2.6"/>
    <rect x="0.6" y="22.5" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -15.9711 8.968)" class="st0" width="4.4" height="2.6"/>
      <rect x="-3.1" y="18.8" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -14.4391 5.2693)" class="st0" width="4.4" height="2.6"/>
    <rect x="-6.8" y="15.1" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -12.907 1.5706)" class="st0" width="4.4" height="2.6"/>
  </g>
  </g>
  </svg>
  </div>
</div>
</template>
<script>
// import { mapState } from 'vuex'
import { getTheme } from '@/utils/theme';
import PhaseDataModel from '@/components/PhaseDataModel';

export default {
  name: 'xdr-dir-selector',
  props: {
    showlist: {
      type: Array,
      default: function () {
        return [
          {
            id: 1,
            color: 'red'
          },
          {
            id: 2,
            color: 'yellow'
          }
        ]
      }
    },
    showlists: {
      type: Number
    },
    phaseOption: {
      type: Array,
      default: () => []
    },
    Width: {// 车道宽
      type: String,
      default: '60px'
    },
    Height: {
      type: String,
      default: '60px'
    },
    Widths: {// 人行横道宽
      type: String,
      default: '50px'
    },
    Heights: {
      type: String,
      default: '50px'
    },
    PathWidth: {
      type: Number,
      default: 18
    },
    ISActiveMask: {
      type: Boolean,
      default: false
    },
    MaskColor: {
      type: String,
      default: '#0096ba'
    },
    Datas: {// 人行横道样式
      type: Object
    },
    Data: {// 车道样式
      type: Object
    }
  },
  computed: {
    // ...mapState({
    //   roadDirection: state => state.globalParam.roadDirection
    // })
  },
  watch: {
    showlist: {
      handler: function (newList) {
        this.refreshShow(newList)
        this.refreshShows(newList)
      },
      deep: true // 深度监听
    },
    phaseOption: {
      handler (val) {
        this.getPedPhasePos(val)
        // this.phaseOption = val
        this.getDir(this.showlists)
      },
      deep: true
    },
    showlists: {
      handler: function (newList) {
        this.getPedPhasePos(this.phaseOption)
        this.getDir(newList)
      },
      deep: true // 深度监听
    }
  },
  data () {
    return {
      maskmark: false,
      sidewalkPhaseData: [],
      dirArr: [],
      peoplestatus: [
        {
          id: 1,
          name: '东西路段人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 2,
          name: '南北路段人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 3,
          name: 'X人行横道-\\',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 4,
          name: 'X人行横道-/',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 5,
          name: '北人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 6,
          name: '南人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 7,
          name: '东人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 8,
          name: '西人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 9,
          name: '西人行横道-下',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 10,
          name: '西人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 11,
          name: '东人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 12,
          name: '东人行横道-下',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 13,
          name: '南人行横道-右',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 14,
          name: '南人行横道-左',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 15,
          name: '北人行横道-右',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 16,
          name: '北人行横道-左',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 17,
          name: '东南人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 18,
          name: '西南人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 19,
          name: '东北人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 20,
          name: '西北人行横道',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 21,
          name: '东南人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 22,
          name: '东南人行横道-下',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 23,
          name: '西南人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 24,
          name: '西南人行横道-下',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 25,
          name: '东北人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 26,
          name: '东北人行横道-下',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 27,
          name: '西北人行横道-上',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 28,
          name: '西北人行横道-下',
          isshow: false,
          color: '#0096ba'
        }
      ],
      status: [
        {
          id: 1,
          name: 'East-Straight',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 2,
          name: 'East-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 3,
          name: 'East-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 4,
          name: 'East-Back ',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 5,
          name: 'West-Straight',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 6,
          name: 'West-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 7,
          name: 'West-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 8,
          name: 'West-Back',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 9,
          name: 'North-Straight',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 10,
          name: 'North-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 11,
          name: 'North-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 12,
          name: 'North-Back',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 13,
          name: 'South-Straight',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 14,
          name: 'South-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 15,
          name: 'South-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 16,
          name: 'South-Back',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 17,
          name: 'Straight-Southeast',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 18,
          name: 'Southeast-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 19,
          name: 'Southeast-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 20,
          name: 'Turn-Southeast',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 21,
          name: 'Straight-Southwest',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 22,
          name: 'Southwest-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 23,
          name: 'Southwest-Right',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 24,
          name: 'Turn-Southwest',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 25,
          name: 'Straight-Northeast',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 26,
          name: 'Northeast-Left',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 27,
          name: 'Northeast-Right-Turn',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 28,
          name: 'Turn-Northeast',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 29,
          name: 'Straight-Northwest',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 30,
          name: 'Northwest-Left-Turn',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 31,
          name: 'Northwest-Right-Turn',
          isshow: false,
          color: '#0096ba'
        },
        {
          id: 32,
          name: 'Turn-Northwest',
          isshow: false,
          color: '#0096ba'
        }
      ]
    }
  },
  created () {
    this.PhaseDataModel = new PhaseDataModel()
    if (this.showlists) {
      this.getPedPhasePos(this.phaseOption)
      this.getDir(this.showlists)
    } else {
      if (this.showlist && this.showlist.length > 0) {
        this.refreshShow(this.showlist)
        this.refreshShows(this.showlist)
      }
    }
  },
  methods: {
    getDir (data) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      this.dirArr = []
      this.phaseOption.filter((item) => {
        if (item.id === data) {
          if (item.direction.length === 0) {
            const recd = {
              id: 0,
              peddirection: this.getPed(item.peddirection),
              color: color
            }
            this.dirArr.push(recd)
          } else {
            for (const rec of item.direction) {
              const recd = {
                id: rec,
                peddirection: this.getPed(item.peddirection),
                color: color
              }
              this.dirArr.push(recd)
            }
          }
        }
      })
      this.refreshShow(this.dirArr)
      this.refreshShows(this.dirArr)
    },
    getPed (data) {
      if (data.length === 0) return
      const ped = []
      let peddirections = []
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      for (const walk of this.sidewalkPhaseData) {
        for (const ped of data) {
          const objs = {}
          objs.name = walk.name
          objs.id = walk.id
          objs.color = color
          if (ped === walk.id) {
            peddirections.push(objs)
            peddirections = Array.from(new Set(peddirections))
          }
        }
      }
      ped.push(...peddirections)
      return ped
    },
    getPedPhasePos (phaseList) {
      // 行人相位信息
      this.sidewalkPhaseData = []
      phaseList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
          // 行人相位
            if (this.PhaseDataModel.getSidePos(dir)) {
              this.sidewalkPhaseData.push({
                // key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name
              })
            }
          })
        }
      })
      return this.sidewalkPhaseData
    },
    randShow () {
      const num1 = parseInt(Math.random() * (15 - 0 + 1) + 0)
      const num2 = parseInt(Math.random() * (15 - 0 + 1) + 0)
      for (let i = 0; i < 16; i++) {
        if (this.status[i].isshow) {
          this.status[i].isshow = false
        }
        if (i === num1 || i === num2) {
          this.status[i].isshow = true
        }
      }
    },
    // 车道
    refreshShow (showList) {
      if (showList.length === 0) return
      if (showList.length > 16) {
        console.log('list can not be bigger than 16!')
      }
      this.maskmark = false
      if (showList.length <= 0) {
        if (this.ISActiveMask) {
          this.maskmark = true
        }
      }

      for (let i = 0; i < 32; i++) {
        if (this.status[i].isshow) {
          this.status[i].isshow = false
        }
      }
      for (let i = 0; i < 32; i++) {
        if (i < showList.length) {
          const id = showList[i].id
          if (!id) return
          // if (id > 32 || id <= 0) {
          //   console.log('Id is invalied!')
          //   continue
          // }
          this.status[id - 1].isshow = true
          this.status[id - 1].color = showList[i].color
        }
      }
    },
    // 人行横道
    refreshShows (showlist) {
      if (showlist.length === 0) return
      for (let i = 0; i < 28; i++) {
        if (this.peoplestatus[i].isshow) {
          this.peoplestatus[i].isshow = false
        }
      }
      if (!showlist[0].peddirection) return
      if (showlist[0].peddirection.length === 0) return
      for (let i = 0; i < showlist[0].peddirection.length; i++) {
        for (let j = 0; j < this.peoplestatus.length; j++) {
          if (showlist[0].peddirection[i].name === this.peoplestatus[j].name) {
            this.peoplestatus[j].isshow = true
            this.peoplestatus[j].color = showlist[0].peddirection[i].color
          }
        }
      }
    }
  }
}
</script>

<style scoped>
svg:not(:root){
  overflow: unset;
}
.invisible {
  visibility: hidden;
}
.st0{color: #040000;}
.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#303133;}
</style>
