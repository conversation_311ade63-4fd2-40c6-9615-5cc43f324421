<template>
    <el-row :gutter="470">
        <el-col :span="6" v-for="indexI in 2" :key="indexI">
            <el-card style="width: 460px">
                <template #header>
                    <div class="card-header">
                        <span>环{{ indexI }}</span>
                    </div>
                </template>
                <div class="form-title">
                    <div>相位</div>
                    <div>属性</div>
                    <div>晚启动</div>
                    <div>早结束</div>
                </div>
                <div class="form-content" v-for="index in 4" :key="index">
                    <div>
                        <el-select v-model="form.phase" placeholder="请选择相位">
                            <el-option label="Zone one" value="shanghai" />
                            <el-option label="Zone two" value="beijing" />
                        </el-select>
                    </div>
                    <div>
                        <el-select v-model="form.attribute" placeholder="请选择属性">
                            <el-option label="协调相位" value="协调相位" />
                            <el-option label="关键相位" value="关键相位" />
                            <el-option label="固定相位" value="固定相位" />
                        </el-select>
                    </div>
                    <div>
                        <el-input v-model="form.delaystart" />
                    </div>
                    <div>
                        <el-input v-model="form.advanceend" />
                    </div>
                </div>
            </el-card>
        </el-col>
    </el-row>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

// do not use same name with ref
const form = reactive({
    phase: '',
    attribute: '',
    delaystart: "",
    advanceend: '',
})
</script>

<style scoped>
.card-header {
    font-size: 14px;
    text-align: center;
}

.form-title {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    /* 添加间距 */
    margin-bottom: 10px;
}

.form-title>div {
    flex: 1;
    text-align: center;
    font-weight: bold;
}

.form-content {
    display: flex;
    gap: 20px;
    /* 与标题保持一致间距 */
    margin-bottom: 15px;
}

.form-content>div {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 保持表单元素宽度自适应 */
.form-content .el-select,
.form-content .el-input {
    width: 100%;
}
</style>