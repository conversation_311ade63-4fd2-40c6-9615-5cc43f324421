<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="信号机名称" prop="signalName">
              <el-input v-model="queryParams.signalName" placeholder="请输入信号机名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="信号机IP" prop="signalIp">
              <el-input v-model="queryParams.signalIp" placeholder="请输入信号机IP" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="信号机协议" prop="signalProtocol">
              <el-select v-model="queryParams.signalProtocol" placeholder="请选择信号机协议" clearable>
                <el-option v-for="dict in utc_signal_protocol" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="信号机厂家" prop="signalVender">
              <el-select v-model="queryParams.signalVender" placeholder="请选择信号机厂家" clearable>
                <el-option v-for="dict in utc_vender_name" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['business:signalManagement:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['business:signalManagement:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['business:signalManagement:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['business:signalManagement:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="signalManagementList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="信号机名称" align="center" prop="signalName" />
        <el-table-column label="信号机IP" align="center" prop="signalIp" />
        <el-table-column label="信号机端口" align="center" prop="signalPort" />
        <el-table-column label="信号机协议" align="center" prop="signalProtocol">
          <template #default="scope">
            <dict-tag :options="utc_signal_protocol" :value="scope.row.signalProtocol" />
          </template>
        </el-table-column>
        <el-table-column label="信号机厂家" align="center" prop="signalVender">
          <template #default="scope">
            <dict-tag :options="utc_vender_name" :value="scope.row.signalVender" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="信号机配置" align="center" prop="signalConfig" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['business:signalManagement:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="配置" placement="top">
              <el-button link type="primary" icon="Setting" @click="drawerVisible = true"
                v-hasPermi="['business:signalManagement:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['business:signalManagement:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改信号机管理对话框 -->
    <el-drawer :title="drawer.title" v-model="drawer.visible" size="50%" append-to-body>
      <el-form ref="signalManagementFormRef" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" placeholder="请输入主键" />
        </el-form-item> -->
        <el-form-item label="信号机名称" prop="signalName">
          <el-input v-model="form.signalName" placeholder="请输入信号机名称" />
        </el-form-item>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="信号机IP" prop="signalIp">
              <el-input v-model="form.signalIp" placeholder="请输入信号机IP" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="信号机端口" prop="signalPort">
              <el-input-number :min="0" :max="65535" v-model="form.signalPort" style="width: 100%;"
                placeholder="请输入信号机端口" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="信号机协议" prop="signalProtocol">
          <el-select v-model="form.signalProtocol" placeholder="请选择信号机协议">
            <el-option v-for="dict in utc_signal_protocol" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信号机厂家" prop="signalVender">
          <el-select v-model="form.signalVender" placeholder="请选择信号机厂家">
            <el-option v-for="dict in utc_vender_name" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信号机配置" prop="signalConfig">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.signalConfig" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
        <el-form-item label="数据" prop="data">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.data" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
        <el-form-item label="环" prop="ring">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.ring" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
        <el-form-item label="指令数据" prop="commondata">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.commondata" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
        <el-form-item label="合并方向" prop="mergedirection">
          <JsonEditorVue style="width: 100%; height: 600px" v-model="form.mergedirection" :language="'zh-CN'"
            :options="{ mode: 'code' }" @validationError="handleValidationError"
            @update:modelValue="handleJsonUpdate" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="drawer-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>

    <el-drawer title="信号机配置" v-model="drawerVisible" size="80%" append-to-body>
      <AllInOne />
    </el-drawer>
  </div>
</template>

<script setup name="SignalManagement" lang="ts">
import { debounce } from 'lodash'

import { listSignalManagement, getSignalManagement, delSignalManagement, addSignalManagement, updateSignalManagement } from '@/api/business/signalManagement';
import { SignalManagementVO, SignalManagementQuery, SignalManagementForm } from '@/api/business/signalManagement/types';
import JsonEditorVue from 'json-editor-vue3'
import AllInOne from './components/AllInOne.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { utc_vender_name, utc_signal_protocol } = toRefs<any>(proxy?.useDict('utc_vender_name', 'utc_signal_protocol'));

const signalManagementList = ref<SignalManagementVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const drawerVisible = ref(false);

const queryFormRef = ref<ElFormInstance>();
const signalManagementFormRef = ref<ElFormInstance>();

const drawer = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SignalManagementForm = {
  id: undefined,
  signalName: undefined,
  signalIp: undefined,
  signalPort: undefined,
  signalProtocol: undefined,
  signalVender: undefined,
  signalConfig: undefined,
}
const data = reactive<PageData<SignalManagementForm, SignalManagementQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    signalName: undefined,
    signalIp: undefined,
    signalProtocol: undefined,
    signalVender: undefined,
    params: {
    }
  },
  rules: {
    signalName: [
      { required: true, message: "信号机名称不能为空", trigger: "blur" }
    ],
    signalIp: [
      { required: true, message: "信号机IP不能为空", trigger: "blur" }
    ],
    signalPort: [
      { required: true, message: "信号机端口不能为空", trigger: "blur" }
    ],
    signalProtocol: [
      { required: true, message: "信号机协议不能为空", trigger: "change" }
    ],
    signalVender: [
      { required: true, message: "信号机厂家不能为空", trigger: "change" }
    ],
    signalConfig: [
      { required: true, message: "信号机配置不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const jsonValid = ref<boolean>(true);

/** 查询信号机管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSignalManagement(queryParams.value);
  signalManagementList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  drawer.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  jsonValid.value = true;
  signalManagementFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: SignalManagementVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  drawer.visible = true;
  drawer.title = "添加信号机管理";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: SignalManagementVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getSignalManagement(_id);
  Object.assign(form.value, res.data);

  if (res.data.signalConfig) {
    const signalConfigStr = res.data.signalConfig.toString();
    form.value.signalConfig = JSON.parse(signalConfigStr);
    jsonValid.value = true;
  }

  drawer.visible = true;
  drawer.title = "修改信号机管理";
}

/** 提交按钮 */
const submitForm = () => {
  signalManagementFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      await nextTick(); // 确保状态更新完成
      if (!jsonValid.value) {
        proxy?.$modal.msgError("信号机配置 JSON 格式错误");
        return;
      }

      buttonLoading.value = true;
      if (form.value.id) {
        await updateSignalManagement(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addSignalManagement(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      drawer.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: SignalManagementVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除信号机管理编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delSignalManagement(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('business/signalManagement/export', {
    ...queryParams.value
  }, `signalManagement_${new Date().getTime()}.xlsx`)
}

const handleValidationError = (errors) => {
  jsonValid.value = false;
}

const handleJsonUpdate = debounce((newJson) => {
  jsonValid.value = true;
}, 300)

onMounted(() => {
  getList();
});
</script>
