
export interface DirectionSelectorProps {
  imgs: DirectionItem[];
  list: number[];
  index: number;
  disabled: boolean;
  showBottomName: boolean;
  lines: number;
  rows: number;
  showSpan: boolean;
  refresh: boolean;
  showDirIcon: boolean;
  laneIconStyle?: IconStyle;
  pedIconStyle?: IconStyle;
}

export interface DirectionSelectorEmits {
  (e: 'finsh', payload: { status: number[]; index: number }): void;
}
