import { useGlobalStoreWithOut } from '@/store/modules/globalParam'
import eastPed from '@/assets/sidewalk_type/east-ped.svg'
import westPed from '@/assets/sidewalk_type/west-ped.svg'
import southPed from '@/assets/sidewalk_type/south-ped.svg'
import northPed from '@/assets/sidewalk_type/north-ped.svg'
import eastTop from '@/assets/sidewalk_type/east-top.svg'
import eastBottom from '@/assets/sidewalk_type/east-bottom.svg'
import westTop from '@/assets/sidewalk_type/west-top.svg'
import westBottom from '@/assets/sidewalk_type/west-bottom.svg'
import southLeft from '@/assets/sidewalk_type/south-left.svg'
import southRight from '@/assets/sidewalk_type/south-right.svg'
import northLeft from '@/assets/sidewalk_type/north-left.svg'
import northRight from '@/assets/sidewalk_type/north-right.svg'
import xRight from '@/assets/sidewalk_type/X-right.svg'
import xLeft from '@/assets/sidewalk_type/X-left.svg'
import southNorthPed from '@/assets/sidewalk_type/south-north-ped.svg'
import eastWestPed from '@/assets/sidewalk_type/east-west-ped.svg'
import southeastCrosswalk from '@/assets/sidewalk_type/southeast-crosswalk.svg'
import southwestCrosswalk from '@/assets/sidewalk_type/southwest-crosswalk.svg'
import northeastCrosswalk from '@/assets/sidewalk_type/northeast-crosswalk.svg'
import northwestCrosswalk from '@/assets/sidewalk_type/northwest-crosswalk.svg'
import southeastCrosswalkUp from '@/assets/sidewalk_type/southeast-crosswalk-up.svg'
import southeastCrosswalkDown from '@/assets/sidewalk_type/southeast-crosswalk-down.svg'
import southwestCrosswalkUp from '@/assets/sidewalk_type/southwest-crosswalk-up.svg'
import southwestCrosswalkDown from '@/assets/sidewalk_type/southwest-crosswalk-down.svg'
import northeastCrosswalkUp from '@/assets/sidewalk_type/northeast-crosswalk-up.svg'
import northeastCrosswalkDown from '@/assets/sidewalk_type/northeast-crosswalk-down.svg'
import northwestCrosswalkUp from '@/assets/sidewalk_type/northwest-crosswalk-up.svg'
import northwestCrosswalkDown from '@/assets/sidewalk_type/northwest-crosswalk-down.svg'


const images = [
  { id: 1, name: '东直行', class: 'iconfont icon-dongzhihang' },
  { id: 2, name: '东左转', class: 'iconfont icon-dongzuozhuan' },
  { id: 3, name: '东右转', class: 'iconfont icon-dongyouzhuan' },
  { id: 4, name: '东掉头', class: 'iconfont icon-dongdiaotou' },
  { id: 5, name: '西直行', class: 'iconfont icon-xizhihang' },
  { id: 6, name: '西左转', class: 'iconfont icon-xizuozhuan' },
  { id: 7, name: '西右转', class: 'iconfont icon-xiyouzhuan' },
  { id: 8, name: '西掉头', class: 'iconfont icon-xidiaotou' },
  { id: 9, name: '北直行', class: 'iconfont icon-beizhihang' },
  { id: 10, name: '北左转', class: 'iconfont icon-beizuozhuan' },
  { id: 11, name: '北右转', class: 'iconfont icon-beiyouzhuan' },
  { id: 12, name: '北掉头', class: 'iconfont icon-beidiaotou' },
  { id: 13, name: '南直行', class: 'iconfont icon-nanzhihang' },
  { id: 14, name: '南左转', class: 'iconfont icon-nanzuozhuan' },
  { id: 15, name: '南右转', class: 'iconfont icon-nanyouzhuan' },
  { id: 16, name: '南掉头', class: 'iconfont icon-nandiaotou' },
  { id: 17, name: '东南直行', class: 'iconfont icon-dongnanzhihang' },
  { id: 18, name: '东南左转', class: 'iconfont icon-dongnanzuozhuan' },
  { id: 19, name: '东南右转', class: 'iconfont icon-dongnanyouzhuan' },
  { id: 20, name: '东南掉头', class: 'iconfont icon-dongnandiaotou' },
  { id: 21, name: '西南直行', class: 'iconfont icon-xinanzhihang' },
  { id: 22, name: '西南左转', class: 'iconfont icon-xinanzuozhuan' },
  { id: 23, name: '西南右转', class: 'iconfont icon-xinanyouzhuan' },
  { id: 24, name: '西南掉头', class: 'iconfont icon-xinandiaotou' },
  { id: 25, name: '东北直行', class: 'iconfont icon-dongbeizhihang' },
  { id: 26, name: '东北左转', class: 'iconfont icon-dongbeizuozhuan' },
  { id: 27, name: '东北右转', class: 'iconfont icon-dongbeiyouzhuan' },
  { id: 28, name: '东北掉头', class: 'iconfont icon-dongbeidiaotou' },
  { id: 29, name: '西北直行', class: 'iconfont icon-xibeizhihang' },
  { id: 30, name: '西北左转', class: 'iconfont icon-xibeizuozhuan' },
  { id: 31, name: '西北右转', class: 'iconfont icon-xibeiyouzhuan' },
  { id: 32, name: '西北掉头', class: 'iconfont icon-xibeidiaotou' }
]
const pedimages = [
  { id: 1, name: '东行人',    img: eastPed },
  { id: 2, name: '西行人',    img: westPed },
  { id: 3, name: '南行人',    img: southPed },
  { id: 4, name: '北行人',    img: northPed },
  { id: 5, name: '东行人-上',    img: eastTop },
  { id: 6, name: '东行人-下',    img: eastBottom },
  { id: 7, name: '西行人-上',    img: westTop },
  { id: 8, name: '西行人-下' ,   img: westBottom},
  { id: 9, name: '南行人-左',    img: southLeft },
  { id: 10, name: '南行人-右',   img: southRight },
  { id: 11, name: '北行人-左',    img: northLeft},
  { id: 12, name: '北行人-右',    img: northRight },
  { id: 13, name: 'X行人-/',    img: xRight },
  { id: 14, name: 'X行人-\\',    img: xLeft },
  { id: 15, name: '东西路段行人',    img: southNorthPed },
  { id: 16, name: '南北路段行人',    img: eastWestPed},
  { id: 17, name: '东南行人',    img: southeastCrosswalk },
  { id: 18, name: '西南行人',    img: southwestCrosswalk },
  { id: 19, name: '东北行人',    img: northeastCrosswalk },
  { id: 20, name: '西北行人',    img: northwestCrosswalk },
  { id: 21, name: '东南行人-上',    img: southeastCrosswalkUp },
  { id: 22, name: '东南行人-下',    img: southeastCrosswalkDown },
  { id: 23, name: '西南行人-上',    img: southwestCrosswalkUp },
  { id: 24, name: '西南行人-下',    img: southwestCrosswalkDown },
  { id: 25, name: '东北行人-上',    img: northeastCrosswalkUp },
  { id: 26, name: '东北行人-下',    img: northeastCrosswalkDown },
  { id: 27, name: '西北行人-上',    img: northwestCrosswalkUp },
  { id: 28, name: '西北行人-下',    img: northwestCrosswalkDown }
]
const getPhase = () => {
  const appStore = useGlobalStoreWithOut()
  if (appStore.roadDirection === 'left') {
    // 左行下，掉头图标替换
    images.forEach((img) => {
      if (img.id === 4) {
        img.class = 'iconfont icon-dongtiaotou-yinni'
      }
      if (img.id === 8) {
        img.class = 'iconfont icon-xitiaotou-yinni'
      }
      if (img.id === 12) {
        img.class = 'iconfont icon-beitiaotou-yinni'
      }
      if (img.id === 16) {
        img.class = 'iconfont icon-nantiaotou-yinni'
      }
    })
  }
  return images
}

export { pedimages, getPhase }
