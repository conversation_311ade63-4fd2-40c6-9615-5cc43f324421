export default {
  // 路由国际化
  route: {
    dashboard: 'Dashboard',
    document: 'Document'
  },
  // 登录页面国际化
  login: {
    selectPlaceholder: 'Please select/enter a company name',
    username: 'Userna<PERSON>',
    password: 'Password',
    login: 'Login',
    logging: 'Logging...',
    code: 'Verification Code',
    rememberPassword: 'Remember me',
    switchRegisterPage: 'Sign up now',
    rule: {
      tenantId: {
        required: 'Please enter your tenant id'
      },
      username: {
        required: 'Please enter your account'
      },
      password: {
        required: 'Please enter your password'
      },
      code: {
        required: 'Please enter a verification code'
      }
    },
    social: {
      wechat: 'Wechat Login',
      maxkey: '<PERSON><PERSON><PERSON> Login',
      topiam: 'TopIam Login',
      gitee: 'Gitee Login',
      github: 'Github Login'
    }
  },
  // 注册页面国际化
  register: {
    selectPlaceholder: 'Please select/enter a company name',
    username: 'Userna<PERSON>',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    register: 'Register',
    registering: 'Registering...',
    registerSuccess: 'Congratulations, your {username} account has been registered!',
    code: 'Verification Code',
    switchLoginPage: 'Log in with an existing account',
    rule: {
      tenantId: {
        required: 'Please enter your tenant id'
      },
      username: {
        required: 'Please enter your account',
        length: 'The length of the user account must be between {min} and {max}'
      },
      password: {
        required: 'Please enter your password',
        length: 'The user password must be between {min} and {max} in length',
        pattern: "Can't contain illegal characters: {strings}"
      },
      code: {
        required: 'Please enter a verification code'
      },
      confirmPassword: {
        required: 'Please enter your password again',
        equalToPassword: 'The password entered twice is inconsistent'
      }
    }
  },
  // 导航栏国际化
  navbar: {
    full: 'Full Screen',
    language: 'Language',
    dashboard: 'Dashboard',
    document: 'Document',
    message: 'Message',
    layoutSize: 'Layout Size',
    selectTenant: 'Select Tenant',
    layoutSetting: 'Layout Setting',
    personalCenter: 'Personal Center',
    logout: 'Logout'
  },
  common: {
    yes: 'yes',
    no: 'no',
    enabled: 'enabled',
    disabled: 'disabled',
    add: 'add',
    edit: 'edit',
    delete: 'delete',
    clone: 'clone',
    view: 'view',
    export: 'export',
    import: 'import',
    preview: 'preview',
    download: 'download',
    upload: 'upload',
    search: 'search',
    reset: 'reset',
    save: 'save',
    cancel: 'cancel',
    confirm: 'confirm',
    submit: 'submit',
    close: 'close',
    operate: 'operate',
    status: 'status',
    remark: 'remark',
    sort: 'sort',
    action: 'action',
    select: 'select',
    input: 'input',
    required: 'required',
    alarm: 'Info',
    addfailed: 'Add Success!',
    addsucess: 'Add Failed!',
    addcancel: 'Add Canceled!',
    deletefailed: 'Delete Failed!',
    deletesucess: 'Delete Success!',
    deletecancel: 'Delete Canceled!',
    updatefailed: 'Update Failed!',
    updatesucess: 'Update Success!',
    updatecancel: 'Update Canceled!',
    relatefailed: 'Relate Failed!',
    relatesucess: 'Relate Success!',
    relatecancel: 'Relate Canceled!',
    savesucess: 'Save Success!',
  }
};
