export interface IntersectionDeviceVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 设备编码
   */
  deviceCode: string;

  /**
   * 设备名称
   */
  deviceName: string;

  /**
   * 设备类型
   */
  deviceType: string;

  /**
   * 设备厂家
   */
  deviceVender: string;

  /**
   * 安装位置
   */
  installPosition: string;

  /**
   * 识别方向
   */
  identifyDirection: string;

  /**
   * 连接方式
   */
  connectType: string;

  /**
   * 设备IP
   */
  deviceIp: string;

  /**
   * 设备端口
   */
  devicePort: number;

  /**
   * 视频地址
   */
  videoUrl: string;

  /**
   * 视频识别范围
   */
  videoIdentifyRange: string;

  /**
   * 视频重点区域
   */
  videoFocusArea: string;

  /**
   * 雷达数据通道
   */
  radarDataChannel: number;

  /**
   * 雷达数据服务
   */
  radarDataService: string;

  /**
   * 雷达斑马线位置
   */
  radarCrossPosition: string;

  /**
   * 设备路口id
   */
  deviceIntersectionId: string | number;

  /**
   * 路口名称
   */
  intersectionName: string;

}

export interface IntersectionDeviceForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 设备编码
   */
  deviceCode?: string;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备类型
   */
  deviceType?: string;

  /**
   * 设备厂家
   */
  deviceVender?: string;

  /**
   * 安装位置
   */
  installPosition?: string;

  /**
   * 识别方向
   */
  identifyDirection?: string;

  /**
   * 连接方式
   */
  connectType?: string;

  /**
   * 设备IP
   */
  deviceIp?: string;

  /**
   * 设备端口
   */
  devicePort?: number;

  /**
   * 视频地址
   */
  videoUrl?: string;

  /**
   * 视频识别范围
   */
  videoIdentifyRange?: string;

  /**
   * 视频重点区域
   */
  videoFocusArea?: string;

  /**
   * 雷达数据通道
   */
  radarDataChannel?: number;

  /**
   * 雷达数据服务
   */
  radarDataService?: string;

  /**
   * 雷达斑马线位置
   */
  radarCrossPosition?: string;

  /**
   * 设备路口id
   */
  deviceIntersectionId?: string | number;

}

export interface IntersectionDeviceQuery extends PageQuery {

  /**
   * 设备编码
   */
  deviceCode?: string;

  /**
   * 设备名称
   */
  deviceName?: string;

  /**
   * 设备类型
   */
  deviceType?: string;

  /**
   * 设备厂家
   */
  deviceVender?: string;

  /**
   * 设备路口id
   */
  deviceIntersectionId?: string | number;

    /**
     * 日期范围参数
     */
    params?: any;
}



