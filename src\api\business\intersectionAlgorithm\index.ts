import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { IntersectionAlgorithmVO, IntersectionAlgorithmForm, IntersectionAlgorithmQuery } from '@/api/business/intersectionAlgorithm/types';

/**
 * 查询路口算法列表
 * @param query
 * @returns {*}
 */

export const listIntersectionAlgorithm = (query?: IntersectionAlgorithmQuery): AxiosPromise<IntersectionAlgorithmVO[]> => {
  return request({
    url: '/business/intersectionAlgorithm/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询路口算法详细
 * @param id
 */
export const getIntersectionAlgorithm = (id: string | number): AxiosPromise<IntersectionAlgorithmVO> => {
  return request({
    url: '/business/intersectionAlgorithm/' + id,
    method: 'get'
  });
};

/**
 * 新增路口算法
 * @param data
 */
export const addIntersectionAlgorithm = (data: IntersectionAlgorithmForm) => {
  return request({
    url: '/business/intersectionAlgorithm',
    method: 'post',
    data: data
  });
};

/**
 * 修改路口算法
 * @param data
 */
export const updateIntersectionAlgorithm = (data: IntersectionAlgorithmForm) => {
  return request({
    url: '/business/intersectionAlgorithm',
    method: 'put',
    data: data
  });
};

/**
 * 删除路口算法
 * @param id
 */
export const delIntersectionAlgorithm = (id: string | number | Array<string | number>) => {
  return request({
    url: '/business/intersectionAlgorithm/' + id,
    method: 'delete'
  });
};
