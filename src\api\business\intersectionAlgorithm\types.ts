export interface IntersectionAlgorithmVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 算法名称
   */
  algorithmName: string;

  /**
   * 算法描述
   */
  algorithmDesc: string;

  /**
   * 算法配置
   */
  algorithmConfig: object;

  /**
   * 算法路口id
   */
  algorithmIntersectionId: string | number;

  /**
 * 路口名称
 */
  intersectionName: string;

}

export interface IntersectionAlgorithmForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 算法名称
   */
  algorithmName?: string;

  /**
   * 算法描述
   */
  algorithmDesc?: string;

  /**
   * 算法配置
   */
  algorithmConfig?: object;

  /**
   * 算法路口id
   */
  algorithmIntersectionId?: string | number;

}

export interface IntersectionAlgorithmQuery extends PageQuery {

  /**
   * 算法名称
   */
  algorithmName?: string;

  /**
   * 算法路口id
   */
  algorithmIntersectionId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



