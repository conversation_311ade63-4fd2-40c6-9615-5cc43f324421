<template>
    <div class="traffic-control">
        <div class="timeline-container">
            <div class="timeline">
                <div v-for="(stage, stageIndex) in plan" :key="stageIndex" class="stage"
                    :style="{ width: `${stage.totalDuration}px` }">
                    <!-- <div class="signImage"> -->
                    <!-- <SignImageView :value="stage.signCodes" image-width="20px" image-height="20px" /> -->
                    <!-- </div> -->
                    <div class="light-info">
                        <span>阶段{{ stage.stageNo }}</span>
                    </div>
                    <div v-for="(phase, phaseIndex) in stage.phaseList" :key="phaseIndex" class="timeline-bar"
                        :style="{ width: phase.percent, backgroundColor: phase.color }">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// import SignImageView from "@/components/SignImageView/index.vue";
import { ref, computed, onMounted } from 'vue';

const stage = ref({
    totalDuration: 120,
    stageNo: 1,
    phaseList: [
        { duration: 60, color: '#7CCC66', percent: '90%' },
        { duration: 5, color: '#F9DC6A', percent: '5%' },
        { duration: 55, color: '#F27979', percent: '5%' },
    ]
})
// const stage = ref({});
const plan = ref([
    stage.value,
    stage.value,
    stage.value
]);
// const plan = ref([]);

function handleData(stageList) {
    plan.value = [];
    console.log(stageList);
    stageList.forEach((item, index) => {
        let baseWidth = 200;
        let greenPercent = ((item.stageTime + baseWidth) / (item.stageTime + baseWidth + 15)) * 100;
        let yellowPercent = (5 / (item.stageTime + baseWidth + 15)) * 100;
        let redPercent = 100 - (greenPercent + yellowPercent);

        let stage = {
            totalDuration: item.stageTime + baseWidth + 15,
            stageNo: item.stageNo,
            phaseList: [
                { duration: item.stageTime, color: '#60ff62', percent: greenPercent + "%" },
                { duration: 5, color: '#fbfe61', percent: yellowPercent + "%" },
                { duration: 10, color: '#ff5f5f', percent: redPercent + '%' },
            ],
            signCodes: item.signCodes
        };

        console.log(stage)
        plan.value.push(stage)
    })
    // console.log(plan)
};

defineExpose({ handleData })
</script>

<style scoped>
.traffic-control {
    text-align: center;
    font-family: Arial, sans-serif;
}

.timeline-container {
    position: relative;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
}

.timeline {
    display: flex;
    flex-wrap: nowrap;
    height: 30px;
}

.stage {
    display: flex;
    flex-wrap: nowrap;
    /* height: 60px; */
    position: relative;
    /* gap: 10px; 项目之间的间距 */
}

.timeline-bar {
    /* 调整高度以适应一行显示 */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.signImage {
    border: 0.5px solid black;
    width: 40px;
    text-align: center;
}

.light-info {
    position: absolute;
    color: black;
    font-size: 14px;
    z-index: 2;
    padding: 2px 5px;
    margin-left: 40px;
}
</style>