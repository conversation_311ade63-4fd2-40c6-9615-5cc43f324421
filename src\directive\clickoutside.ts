/**
 * v-clickOutside 点击外部指令
 * 当点击元素外部时触发回调函数
 */
import { DirectiveBinding } from 'vue';

interface ClickOutsideElement extends HTMLElement {
  __vueClickOutside__?: (e: Event) => void;
}

interface ClickOutsideBinding extends DirectiveBinding {
  value?: (e: Event) => void;
}

export default {
  mounted(el: ClickOutsideElement, binding: ClickOutsideBinding) {
    function documentHandler(e: Event) {
      if (el.contains(e.target as Node)) {
        return false;
      }
      if (binding.value && typeof binding.value === 'function') {
        binding.value(e);
      }
    }
    el.__vueClickOutside__ = documentHandler;
    document.addEventListener('click', documentHandler);
  },
  unmounted(el: ClickOutsideElement) {
    if (el.__vueClickOutside__) {
      document.removeEventListener('click', el.__vueClickOutside__);
      delete el.__vueClickOutside__;
    }
  }
};
