import { Road, ConfigData } from './types';

/**
 * 默认十字路口配置
 */
export const defaultCrossConfig: Road[] = [
  {
    "roadId": 1,
    "roadName": "北向路",
    "direction": {
      "id": 1,
      "name": "北"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 2,
    "roadName": "东向路",
    "direction": {
      "id": 3,
      "name": "东",
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 3,
    "roadName": "南向路",
    "direction": {
      "id": 5,
      "name": "南",
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 4,
    "roadName": "西向路",
    "direction": {
      "id": 7,
      "name": "西",
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  }
];

/**
 * 默认T型路口配置
 */
export const defaultTConfig: Road[] = [
  {
    "roadId": 1,
    "roadName": "北向路",
    "direction": {
      "id": 1,
      "name": "北"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 2,
    "roadName": "东向路",
    "direction": {
      "id": 3,
      "name": "东"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 3,
    "roadName": "南向路",
    "direction": {
      "id": 5,
      "name": "南"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  }
];

/**
 * 默认Y型路口配置
 */
export const defaultYConfig: Road[] = [
  {
    "roadId": 1,
    "roadName": "北向路",
    "direction": {
      "id": 1,
      "name": "北"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 2,
    "roadName": "东南向路",
    "direction": {
      "id": 4,
      "name": "东南"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  },
  {
    "roadId": 3,
    "roadName": "西南向路",
    "direction": {
      "id": 6,
      "name": "西南"
    },
    "dividerType": '2',
    "turnAround": '0',
    "waitTurnLeft": false,
    "waitStraight": false,
    "approach": {
      "lanes": [{
        "nThrough": 0,
        "nTurnLeft": 1,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      }, {
        "nThrough": 0,
        "nTurnLeft": 0,
        "nTurnRight": 1,
        "nTurnAround": 0
      }]
    },
    "exit": {
      "lanes": 3
    },
    "zebraCrossingFlag": true
  }
];

/**
 * 获取默认配置
 * @param type 路口类型
 * @returns 对应类型的默认配置
 */
export function getDefaultConfig(type: string): Road[] {
  switch (type) {
    case 'T':
      return JSON.parse(JSON.stringify(defaultTConfig));
    case 'Y':
      return JSON.parse(JSON.stringify(defaultYConfig));
    case 'cross':
    default:
      return JSON.parse(JSON.stringify(defaultCrossConfig));
  }
}

/**
 * 获取完整配置数据
 * @param type 路口类型
 * @returns 完整配置数据对象
 */
export function getConfigData(type: string): ConfigData {
  return {
    type,
    config: getDefaultConfig(type)
  };
}
