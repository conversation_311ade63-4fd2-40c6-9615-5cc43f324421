const setPhaseBarrier = (rings, tableList) => {
  // 按环序分组
  const ringlength: any = []
  const ringsequence = rings.map((item:any) => {
    if (item.length === 0) return
    ringlength.push(item.length)
    return item.map((k) => {
      return k.id
    })
  })
  const newRings = ringsequence.filter((item) => item)
  const minLength = Math.min(...ringlength)
  const resultArrs: any = [] // 环1分组
  const resultArr: any = [] // 环2分组
  for (let j = 0; j < ringsequence[0].length; j += minLength) {
    resultArrs.push(ringsequence[0].slice(j, j + minLength))
    if (ringsequence[0].length === minLength) {
      resultArrs.push([])
    }
  }
  if (newRings.length > 1) {
    for (let i = 0; i < ringsequence[1].length; i += minLength) {
      resultArr.push(ringsequence[1].slice(i, i + minLength))
    }
  }
  const currentArr: any = []
  // let newPattern = []
  // rings.map(i => {
  //   newPattern.push(...i)
  // })
  if (tableList.length === 0) return
  for (const patternStatus of rings[0]) {
    // if (patternStatus.mode !== 7) {
    const concurrent = tableList.filter((item) => {
      return item.id === patternStatus.id // patternStatus.id当前相位id concurrent当前相位的并发相位
    })[0].newconcurrent // 当前相位的并发相位
    if (concurrent) {
      const obj = {
        id: patternStatus.id,
        current: concurrent.sort()
      }
      currentArr.push(obj)
    }
    // }
  }
  const newCurrent:any = tranform(currentArr)
  // 取没个环与环关系的并集
  const barrierRing1:any = []
  const barrierRing2:any = []
  const ring1: any = [] // 环1与关系1的并集
  for (let h = 0; h < newCurrent.length; h++) {
    const adds = [...new Set(resultArrs[0])].filter((item) => newCurrent[h].includes(item))
    ring1.push(adds)
  }
  const ring1s: any = [] // 环1与关系1的并集
  for (let h = 0; h < newCurrent.length; h++) {
    const addRing1 = [...new Set(resultArrs[1])].filter((item) => newCurrent[h].includes(item))
    ring1s.push(addRing1)
  }
  barrierRing1.push(...ring1, ...ring1s)
  const resultList:any = []
  if (newRings.length > 1) {
    const ring2s:any = [] // 环2与关系1的并集
    for (let h = 0; h < newCurrent.length; h++) {
      const addRing2 = [...new Set(resultArr[1])].filter((item) => newCurrent[h].includes(item))
      ring2s.push(addRing2)
    }
    const ring2:any = [] // 环2与关系1的并集
    for (let h = 0; h < newCurrent.length; h++) {
      const add = [...new Set(resultArr[0])].filter((item) => newCurrent[h].includes(item))
      ring2.push(add)
    }
    barrierRing2.push(...ring2, ...ring2s)
    for (let k = 0; k < barrierRing1.length; k++) {
      resultList.push(barrierRing1[k].concat(barrierRing2[k]))
    }
  }
  const resultLists = resultList.filter((item) => item.length !== 0)
  const ringArr:any = []
  for (let l = 0; l < resultLists.length; l++) {
    for (let d = 0; d < newCurrent.length; d++) {
      if (newCurrent[d].filter((item) => resultLists[l].includes(item)).length > 0) {
        ringArr.push({
          data: resultLists[l],
          id: d + 1,
          index: l
        })
      }
    }
  }
  // let ringObj = {}
  // ringArr.forEach(item => {
  //   ringObj[item.index] = {
  //     id: item.id,
  //     index: item.index
  //   }
  // })
  for (let i = 0; i < ringArr.length; i++) {
    for (let j = i + 1; j < ringArr.length; j++) {
      if (ringArr[i].id === ringArr[j].id && ringArr[i].index + 1 === ringArr[j].index) {
        ringArr[i].data = ringArr[i].data.concat(ringArr[j].data)
        ringArr.splice(ringArr.indexOf(ringArr[j].id), 1)
      }
    }
  }
  const resArr = ringArr.map((item) => item.data)
  const ringTeam = step1(tableList, resArr)
  const ringTeams = ringTeam.filter((item) => item.length !== 0)
  return setBarrier(ringTeams, rings)
}
function check(arr1, arr2) {
  return arr1.some((a1) => arr2.some((a2) => a1 === a2))
}
function setBarrier(ringTeam, val) {
  // 添加特征参数barrier
  const patternObjs = {}
  val.forEach((l) => {
    l.map((k) => {
      patternObjs[k.id] = k.value
    })
  })
  const ret = ringTeam.map((y, index) => {
    if (!y || y.length === 0) return
    // y.map(n => {
    //   n.length = n.data.length > 1 ? n.data.reduce((pre, cur) => pre + patternObjs[cur], 0) : patternObjs[n.data[0]]
    // })
    return {
      barrier: index + 1,
      // length: y[0].length,
      items: y.map((j) => {
        return {
          ring: j.newring,
          data: j.data
        }
      })
    }
  })
  return ret
}
function tranform(arr) {
  // 分barrier
  const newMap = new Map()
  arr.forEach(({ id, current }) => {
    const find = newMap.get(current.join())
    if (find) newMap.get(current.join()).push(id)
    else newMap.set(current.join(), [id])
  })
  const ret:any = []
  newMap.forEach((value, key) => {
    ret.push(Array.from(new Set(key.split(',').map(Number).concat(value))))
  })
  const result:any = []
  for (const a of ret) {
    let merged = false
    for (const r of result) {
      if (check([...r], a)) {
        a.forEach((item) => r.add(item))
        merged = true
      }
    }
    if (!merged) {
      result.push(new Set(a))
    }
    merged = false
  }
  return result.map((s) => [...s])
}
function step1(list, arr) {
  const ret:any = []
  const listObj = {}
  list.forEach((l) => {
    listObj[l.id] = l.newring
  })
  arr.forEach((a) => {
    const retItem:any = []
    a.forEach((b) => {
      if (listObj[b]) {
        const find = retItem.find((r) => r.newring === listObj[b])
        if (find) find.data.push(b)
        else retItem.push({ newring: listObj[b], data: [b] })
      }
    })
    ret.push(retItem)
  })
  return ret
}
export { setPhaseBarrier }
