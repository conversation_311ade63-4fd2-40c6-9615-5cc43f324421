<template>
  <div class="app-container" ref="channelContainer">
    <el-dialog
      width="770px"
      top="6vh"
      v-model="diagramVisible"
      title="一灯一线规范通道接线图"
      :close-on-click-modal="false"
      custom-class="WiringDiagram"
      @close="closeWiringDiagram"
      append-to-body>
      <WiringDiagram />
    </el-dialog>
    <el-button style="margin-bottom:10px" type="primary" @click="onAdd">添加</el-button>
    <el-button style="margin-bottom:10px" type="primary" @click="deleteAllData">全部删除</el-button>
    <el-button style="margin-bottom:10px" type="primary" @click="openWiringDiagram">推荐接线图</el-button>
    <el-table class="tb-edit" ref="singleTable" :data="tscParam.channelList" v-loading="listLoading" element-loading-text="Loading" fit highlight-current-row :max-height="tableHeight" v-click-outside="cancelTable" id="footerBtn">
      <el-table-column align="center" label='ID' min-width="40">
        <template #default="scope">
          <el-input-number size="small" controls-position="right" :min="0"  :step="1" v-model="scope.row.id"  @change="onIdChange(scope.$index, scope.row)" style="width: 100px;"></el-input-number>
          <span>{{scope.row.id}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="控制类型" prop="typeAndSouce" min-width="200">
        <template #default="scope">
          <el-cascader
            size="default"
            expand-trigger="hover"
            :options="typeOptions"
            v-model="scope.row.typeAndSouce"
            class="config"
            clearable
            @change="handleChange(scope.row.typeAndSouce, scope.$index, scope.row)">
          </el-cascader>
          <span v-text="getTypeAndSouce(scope.row)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="类型" prop="direction" min-width="100">
        <template #default="scope">
          <el-select @clear="clearType" v-model="scope.row.type" @change="typeChange(scope.row,scope.$index)" placeholder="请选择" size="small" clearable>
            <el-option
              v-for="item in channeltypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <span v-text="getControlTypestr(scope.row)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="方向" prop="direction" min-width="140">
        <template #default="scope">
          <el-select v-model="scope.row.realdir" @focus="turnChange(scope.row.type,scope.$index)" multiple placeholder="请选择" size="small" @clear="clearRealdir" clearable>
            <el-option
              v-for="item in realdirOptions.get(scope.row.id)"
              :key="item.value"
              :label="item.label"
              :value="item.value">
              <div class="single-model" >
                <XRDDirSelector Width="60px" Height="60px" :showlist="item.item"></XRDDirSelector>
                <span style="float: right; margin-left: 10px; color: #8492a6; font-size: 13px">{{ item.label }}</span>
              </div>
            </el-option>
          </el-select>
          <span>
            <XRDDirSelector :Data="Data" :Datas="Datas" v-if="scope.row.realdir && scope.row.realdir.length > 0" Width="60px" Height="60px" :showlist="getDirectionList(scope.row)"></XRDDirSelector>
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="灯电压检测阈值" min-width="140">
        <template #default="scope">
          <el-input-number size="small" controls-position="right" :min="5" :max="200" :step="1" v-model.number="scope.row.voltthresh" style="width: 100px;"></el-input-number>
          <span>{{scope.row.voltthresh}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="灯功率检测阈值" min-width="140">
        <template #default="scope">
          <el-input-number size="small" controls-position="right" :min="1" :max="2000" :step="1" v-model.number="scope.row.pacthresh" style="width: 100px;"></el-input-number>
          <span>{{scope.row.pacthresh}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="高功率故障阈值" min-width="140">
        <template #default="scope">
          <el-input-number size="small" controls-position="right" :min="100" :max="1000" :step="1" v-model.number="scope.row.peakhthresh" style="width: 100px;"></el-input-number>
          <span>{{scope.row.peakhthresh}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="低功率故障阈值" min-width="140">
        <template #default="scope">
          <el-input-number size="small" controls-position="right" :min="100" :max="1000" :step="1" v-model.number="scope.row.peaklthresh" style="width: 100px;"></el-input-number>
          <span>{{scope.row.peaklthresh}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="灯杆方位" prop="direction" min-width="100">
        <template #default="scope">
          <el-select v-model="scope.row.direction" placeholder="请选择" size="small" clearable @clear="clearValue">
            <el-option
              v-for="item in signLocationList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <span v-text="getDirection(scope.row)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="灯盘方向" prop="direction" min-width="100">
        <template #default="scope">
          <el-select v-model="scope.row.turn" @focus="turnChange(scope.row.type,scope.$index)" multiple placeholder="请选择" size="small" clearable>
            <el-option
              v-for="item in turnOptions.get(scope.row.id)"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <span v-text="getTurn(scope.row, scope.$index)"></span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button link @click="handleClone(scope.$index,scope.row)">克隆</el-button>
          <el-button link @click="handleDelete(scope.$index,scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { XRDDirSelector } from '@/components/XRDDirSelector';
import { getTheme } from '@/utils/theme';
import PhaseDataModel from '@/components/PhaseDataModel';
import { getAllControSource, getOverLap, refreshChannelLockDescData, refreshControlPanelDescData } from '@/utils/channeldesc';
import { getPhaseDesc } from '@/utils/phasedesc';
import { getPedPhaseDesc } from '@/utils/pedphasedesc';
import WiringDiagram from '@/components/WiringDiagram/index.vue';
import { useGlobalStore } from '@/store/modules/globalParam';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getGlobalParamsMgr } from '@/components/EdgeMgr/globalManager';

// 导入类型和数据
import type {
  ChannelItem,
  TypeOption,
  DirectionItem,
  PedestrianDirection,
  SidewalkPhaseData,
  RealDirOption,
  ClickOutsideElement,
  ClickOutsideBinding
} from './types';
import {
  channelTypeOptions,
  carList,
  peopleList,
  turnList,
  signLocationList,
  baseTypeOptions,
  defaultChannelInitData,
  defaultStyleData
} from './datas';

// 导入工具函数
import {
  getControlTypestr,
  getTurn,
  getRealdir,
  getDirectionList,
  getPeds,
  getDirection,
  getTypeAndSouce,
  getPedPhasePos,
  getPed,
  generatePhaseStatusOptions,
  compareProperty,
  clearEmptyValues,
  clearEmptyArrays,
  increaseId
} from './utils';

export default defineComponent({
  name: 'Channel',
  components: {
    XRDDirSelector,
    WiringDiagram
  },
  setup() {
    const globalStore = useGlobalStore();
    const { tscParam, channelDescMap } = storeToRefs(globalStore);

    return {
      globalStore,
      tscParam,
      channelDescMap
    };
  },

  data() {
    return {
      // 组件引用
      channelContainer: null as HTMLElement | null,
      singleTable: null as any,

      // 基础数据
      phaseDataModel: null as PhaseDataModel | null,
      globalParamModel: null as any,

      // 表格配置
      tableHeight: 760,
      listLoading: false,

      // 通道配置
      id: 1,
      typeOptions: [] as TypeOption[],
      sidewalkPhaseData: [] as SidewalkPhaseData[],

      // 样式配置
      Data: defaultStyleData,
      Datas: defaultStyleData,

      // 选项配置
      channelTypeOptions,
      turnOptions: new Map<number, any[]>(),
      realdirOptions: new Map<number, RealDirOption[]>(),
      carList,
      peopleList,
      turnList,
      signLocationList,

      // 描述映射
      desclist: new Map<number, string[]>(),

      // 对话框状态
      diagramVisible: false,
    };
  },

  directives: {
    clickOutside: {
      mounted(el: ClickOutsideElement, binding: ClickOutsideBinding) {
        function documentHandler(e: Event) {
          if (el.contains(e.target as Node)) {
            return false;
          }
          if (binding.expression && binding.value) {
            binding.value(e);
          }
        }
        el.__vueClickOutside__ = documentHandler;
        document.addEventListener('click', documentHandler);
      },
      unmounted(el: ClickOutsideElement) {
        if (el.__vueClickOutside__) {
          document.removeEventListener('click', el.__vueClickOutside__);
          delete el.__vueClickOutside__;
        }
      }
    }
  },

  computed: {
    // 类型选项计算属性
    TypeOption() {
      const arrays: TypeOption[] = [];
      baseTypeOptions.forEach(v => {
        const obj = Object.assign({}, v);
        arrays.push(obj);
      });
      return arrays;
    },

    // 当前通道列表（从store获取）
    list() {
      return this.globalParamModel ? this.globalParamModel.getParamsByType('channelList') : [];
    },

    // 通道类型选项（带国际化）
    channeltypeOptions() {
      return this.channelTypeOptions;
    },

  },

  watch: {
    // 监听通道列表变化
    list: {
      handler: function (val) {
        this.init();
        this.createCurrentDescMap();
      },
      deep: true
    },

    // 监听通道描述映射变化
    channelDescMap: {
      handler() {
        refreshChannelLockDescData();
        refreshControlPanelDescData();
      },
      deep: true
    }
  },

  created() {
    // 初始化相位数据模型
    this.phaseDataModel = new PhaseDataModel();

    // 使用全局参数管理器
    this.globalParamModel = getGlobalParamsMgr();

    // 初始化数据
    this.init();

    // 创建描述映射
    this.createCurrentDescMap();
  },

  mounted() {
    // nextTick(() => {
    //   const container = this.$refs['channelContainer'] as HTMLElement;
    //   if (container) {
    //     this.tableHeight = container.offsetHeight - 90;
    //   }
    //
    //   window.onresize = () => {
    //     if (container) {
    //       this.tableHeight = container.offsetHeight - 90;
    //     }
    //   };
    // });
  },

  methods: {
    // ==================== 工具函数封装 ====================

    /**
     * 获取控制类型字符串
     */
    getControlTypestr(val: ChannelItem): string {
      return getControlTypestr(val, this.channelTypeOptions);
    },

    /**
     * 获取转向描述
     */
    getTurn(val: ChannelItem, index: number): string {
      return getTurn(val, this.turnOptions);
    },

    /**
     * 获取实际方向描述
     */
    getRealdir(val: ChannelItem, index: number): string {
      return getRealdir(val, this.realdirOptions);
    },

    /**
     * 获取方向列表
     */
    getDirectionList(val: ChannelItem): DirectionItem[] {
      return getDirectionList(val);
    },

    /**
     * 获取方向描述
     */
    getDirection(val: ChannelItem): string {
      return getDirection(val, this.signLocationList);
    },

    /**
     * 获取类型和来源描述
     */
    getTypeAndSouce(ele: ChannelItem): string {
      return getTypeAndSouce(ele, this.typeOptions);
    },

    // ==================== 数据处理方法 ====================

    /**
     * ID变化处理
     */
    onIdChange(index: number, row: ChannelItem) {
      const id = row.id;
      const channelList = this.globalParamModel.getParamsByType('channelList');
      const list = channelList.filter(item => item.id === id);
      if (list && list.length > 1) {
        const msg = '重复ID: ' + id;
        ElMessage.warning(msg);
      }
    },

    /**
     * 取消表格选择
     */
    cancelTable(e: Event) {
      this.singleTable?.setCurrentRow();
    },

    /**
     * 清除值
     */
    clearValue() {
      const channel = this.globalParamModel.getParamsByType('channelList');
      clearEmptyValues(channel, 'direction');
    },

    /**
     * 清除实际方向
     */
    clearRealdir() {
      const channel = this.globalParamModel.getParamsByType('channelList');
      clearEmptyArrays(channel, 'realdir');
    },

    /**
     * 清除类型
     */
    clearType() {
      const channel = this.globalParamModel.getParamsByType('channelList');
      clearEmptyValues(channel, 'type');
    },

    // ==================== 业务逻辑方法 ====================

    /**
     * 转向变化处理
     */
    turnChange(val: number | undefined, index: number) {
      if (val !== undefined) {
        const channel = this.globalParamModel.getParamsByType('channelList');
        if (channel && channel[index]) {
          const channelId = channel[index].id;
          this.turnOptions.set(channelId, []);

          // 使用与 typeChange 相同的逻辑
          for (let i = 0; i < this.channelTypeOptions.length; i++) {
            if (this.channelTypeOptions[i].value === val) {
              // 安全检查 turnList 和 children
              if (this.turnList &&
                  this.channelTypeOptions[i].value >= 0 &&
                  this.channelTypeOptions[i].value < this.turnList.length &&
                  this.turnList[this.channelTypeOptions[i].value] &&
                  this.turnList[this.channelTypeOptions[i].value].children) {
                this.turnOptions.set(channelId, this.turnList[this.channelTypeOptions[i].value].children);
              }
              break;
            }
          }
        }
      }
    },

    /**
     * 类型变化处理
     */
    typeChange(val: ChannelItem, index: number) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
      this.turnOptions.set(val.id, []);
      const channel = this.globalParamModel.getParamsByType('channelList');
      const overlap = this.globalParamModel.getParamsByType('overlaplList');

      if (channel[index]) {
        channel[index].turn = [];

        // 设置转向选项
        for (let i = 0; i < this.channelTypeOptions.length; i++) {
          if (this.channelTypeOptions[i].value === val.type) {
            // 安全检查 turnList 和 children
            if (this.turnList &&
                this.channelTypeOptions[i].value >= 0 &&
                this.channelTypeOptions[i].value < this.turnList.length &&
                this.turnList[this.channelTypeOptions[i].value] &&
                this.turnList[this.channelTypeOptions[i].value].children) {
              this.turnOptions.set(val.id, this.turnList[this.channelTypeOptions[i].value].children);
            }
          }
        }

        if (val.controltype === 6 && val.type === 0) {
          // 车道灯处理
          const status = generatePhaseStatusOptions();
          channel[index].realtype = 'direction';
          this.realdirOptions.set(val.id, status);
        } else {
          // 其他类型处理
          this.handleOtherChannelTypes(val, index, channel, overlap, color);
        }

        // 如果只有一个选项，自动选择
        if (this.realdirOptions.get(val.id)?.length === 1) {
          channel[index].realdir = [this.realdirOptions.get(val.id)![0].value];
        }
      }
    },

    /**
     * 处理其他通道类型
     */
    handleOtherChannelTypes(val: ChannelItem, index: number, channel: any[], overlap: any[], color: string) {
      if (val.controltype !== 6 && (val.type === 0 || val.type === 1 || val.type === 3)) {
        // 机动车类型
        channel[index].realdir = [];
        channel[index].realtype = 'direction';

        if (val.controltype === 4 || val.controltype === 5) {
          this.handleOverlapDirection(val, overlap, color);
        } else {
          this.handlePhaseDirection(val, color);
        }
      } else if (val.controltype !== 6 && val.type === 2) {
        // 行人类型
        channel[index].realdir = [];
        channel[index].realtype = 'peddirection';

        if (val.controltype === 4 || val.controltype === 5) {
          this.handleOverlapPedDirection(val, overlap, color);
        } else {
          this.handlePhasePedDirection(val, color);
        }
      } else {
        // 其他类型
        channel[index].realtype = '';
        channel[index].realdir = [];
        this.realdirOptions.set(val.id, []);
      }
    },

    /**
     * 处理跟随相位方向
     */
    handleOverlapDirection(val: ChannelItem, overlap: any[], color: string) {
      const overlapItem = overlap.filter((phase: any) => phase.id === val.controlsource)[0];
      if (!overlapItem?.direction) {
        this.realdirOptions.set(val.id, []);
        return;
      }
      this.realdirOptions.set(val.id, overlapItem.direction.map((item: number) => ({
        value: item,
        item: [{ id: item, peddirection: [], color: color }],
        label: this.getPhaseDesc([item])
      })));
    },

    /**
     * 处理相位方向
     */
    handlePhaseDirection(val: ChannelItem, color: string) {
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const phaseItem = phaseList.filter((phase: any) => phase.id === val.controlsource)[0];
      if (phaseItem?.direction) {
        this.realdirOptions.set(val.id, phaseItem.direction.map((item: number) => ({
          value: item,
          item: [{ id: item, peddirection: [], color: color }],
          label: this.getPhaseDesc([item])
        })));
      }
    },

    /**
     * 处理跟随相位行人方向
     */
    handleOverlapPedDirection(val: ChannelItem, overlap: any[], color: string) {
      const overlapItem = overlap.filter((phase: any) => phase.id === val.controlsource)[0];
      if (!overlapItem?.peddirection) {
        this.realdirOptions.set(val.id, []);
        return;
      }
      this.realdirOptions.set(val.id, overlapItem.peddirection.map((item: number) => ({
        value: item,
        item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
        label: this.getPedPhaseDesc([item])
      })));
    },

    /**
     * 处理相位行人方向
     */
    handlePhasePedDirection(val: ChannelItem, color: string) {
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const phaseItem = phaseList.filter((phase: any) => phase.id === val.controlsource)[0];
      if (phaseItem?.peddirection) {
        this.realdirOptions.set(val.id, phaseItem.peddirection.map((item: number) => ({
          value: item,
          item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
          label: this.getPedPhaseDesc([item])
        })));
      }
    },

    // ==================== 初始化和数据管理 ====================

    /**
     * 初始化（优化版本）
     */
    init() {
      // 初始化数据和选项
      this.initData();

      // 更新当前最大ID
      this.increaseId();

      // 处理通道数据的相关选项
      this.processChannelOptionsData();
    },

    /**
     * 处理通道选项数据（从initData中分离出来）
     */
    processChannelOptionsData() {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
      const channels = this.globalParamModel.getParamsByType('channelList');

      // 为每个通道处理选项数据
      channels.forEach((channel: ChannelItem) => {
        if (channel.id) {
          // 初始化转向选项
          this.turnOptions.set(channel.id, []);
          this.realdirOptions.set(channel.id, []);

          // 根据通道类型设置相应选项
          if (channel.type !== undefined) {
            this.processChannelTypeOptions(channel, color);
          }
        }
      });
    },

    /**
     * 处理单个通道的类型选项
     */
    processChannelTypeOptions(channel: ChannelItem, color: string) {
      // 根据通道类型设置转向选项
      if (channel.type !== undefined &&
          this.turnList &&
          channel.type >= 0 &&
          channel.type < this.turnList.length &&
          this.turnList[channel.type]) {
        this.turnOptions.set(channel.id, this.turnList[channel.type].children || []);
      }

      // 处理实际方向选项（根据控制类型）
      if (channel.controltype !== undefined && channel.controlsource !== undefined) {
        this.processControlTypeOptions(channel, color);
      }
    },

    /**
     * 处理控制类型选项
     */
    processControlTypeOptions(channel: ChannelItem, color: string) {
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const overlap = this.globalParamModel.getParamsByType('overlaplList');

      switch (channel.controltype) {
        case 2: // 机动车相位
        case 3: // 行人相位
          this.setupPhaseOptions(channel, phaseList, color);
          break;
        case 4: // 跟随相位
        case 5: // 行人跟随相位
          this.setupOverlapOptions(channel, overlap, color);
          break;
        case 6: // 车道灯
          this.setupLaneOptions(channel, color);
          break;
        default:
          this.realdirOptions.set(channel.id, []);
      }
    },

    /**
     * 设置相位选项
     */
    setupPhaseOptions(channel: ChannelItem, phaseList: any[], color: string) {
      const phaseItem = phaseList.find(phase => phase.id === channel.controlsource);
      if (phaseItem) {
        if (channel.controltype === 2 && phaseItem.direction) {
          // 机动车相位
          this.realdirOptions.set(channel.id, phaseItem.direction.map((item: number) => ({
            value: item,
            item: [{ id: item, color: color }],
            label: getPhaseDesc([item])
          })));
        } else if (channel.controltype === 3 && phaseItem.peddirection) {
          // 行人相位
          this.realdirOptions.set(channel.id, phaseItem.peddirection.map((item: number) => ({
            value: item,
            item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
            label: this.getPedPhaseDesc([item])
          })));
        }
      }
    },

    /**
     * 设置跟随相位选项
     */
    setupOverlapOptions(channel: ChannelItem, overlap: any[], color: string) {
      const overlapItem = overlap.find(item => item.id === channel.controlsource);
      if (overlapItem) {
        if (channel.controltype === 4 && overlapItem.direction) {
          // 跟随相位
          this.realdirOptions.set(channel.id, overlapItem.direction.map((item: number) => ({
            value: item,
            item: [{ id: item, color: color }],
            label: getPhaseDesc([item])
          })));
        } else if (channel.controltype === 5 && overlapItem.peddirection) {
          // 行人跟随相位
          this.realdirOptions.set(channel.id, overlapItem.peddirection.map((item: number) => ({
            value: item,
            item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
            label: this.getPedPhaseDesc([item])
          })));
        }
      }
    },

    /**
     * 设置车道灯选项
     */
    setupLaneOptions(channel: ChannelItem, color: string) {
      this.realdirOptions.set(channel.id, generatePhaseStatusOptions());
    },

    /**
     * 获取行人相位描述
     */
    getPedPhaseDesc(phaseIds: number[]): string {
      return getPedPhaseDesc(phaseIds);
    },

    /**
     * 初始化数据
     */
    initData() {
      const phasetype = getAllControSource();
      const patterntype = getOverLap();

      // 设置类型选项
      this.typeOptions = [
        { value: 2, label: '机动车相位' },
        { value: 3, label: '行人相位' },
        { value: 4, label: '跟随相位' },
        { value: 5, label: '行人跟随相位' },
        { value: 6, label: '车道灯' },
        { value: 0, label: '不启用' }
      ];

      if (phasetype) {
        this.typeOptions[0].children = phasetype;
        this.typeOptions[1].children = phasetype;
      }
      if (patterntype) {
        this.typeOptions[2].children = patterntype;
        this.typeOptions[3].children = patterntype;
      }
    },

    /**
     * 处理通道数据
     */
    processChannelData(color: string) {
      const channel = this.globalParamModel.getParamsByType('channelList');
      const overlap = this.globalParamModel.getParamsByType('overlaplList');
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const channelMap: { [key: number]: ChannelItem } = {};

      for (const obj of channel) {
        channelMap[obj.id] = obj;
      }

      for (const id in channelMap) {
        const obj = channelMap[id];
        this.processChannelItem(obj, overlap, phaseList, color);
      }
    },

    /**
     * 处理单个通道项
     */
    processChannelItem(obj: ChannelItem, overlap: any[], phaseList: any[], color: string) {
      const list: number[] = [];
      list.push(obj.controltype!);

      if (obj.controltype !== 6 && obj.controltype !== 0) {
        list.push(obj.controlsource!);
      }

      // 获取行人相位数据
      if (obj.controltype === 4 || obj.controltype === 5) {
        this.sidewalkPhaseData = getPedPhasePos(overlap);
      } else {
        this.sidewalkPhaseData = getPedPhasePos(phaseList);
      }

      // 根据类型处理
      if (obj.type === 0 || obj.type === 1 || obj.type === 3) {
        this.processVehicleChannel(obj, overlap, phaseList, color);
      } else if (obj.type === 2) {
        this.processPedestrianChannel(obj, overlap, phaseList, color);
      }

      obj.typeAndSouce = list;
    },

    /**
     * 处理机动车通道
     */
    processVehicleChannel(obj: ChannelItem, overlap: any[], phaseList: any[], color: string) {
      this.turnOptions.set(obj.id, this.carList);
      obj.realtype = 'direction';

      if (obj.controltype === 4 || obj.controltype === 5) {
        this.processOverlapVehicle(obj, overlap, color);
      } else if (obj.controltype === 6) {
        this.processLaneLights(obj, color);
      } else {
        this.processPhaseVehicle(obj, phaseList, color);
      }
    },

    /**
     * 处理行人通道
     */
    processPedestrianChannel(obj: ChannelItem, overlap: any[], phaseList: any[], color: string) {
      obj.realtype = 'peddirection';
      this.turnOptions.set(obj.id, this.peopleList);

      if (obj.controltype === 4 || obj.controltype === 5) {
        this.processOverlapPedestrian(obj, overlap, color);
      } else {
        this.processPhasePedestrian(obj, phaseList, color);
      }
    },

    /**
     * 处理跟随相位机动车
     */
    processOverlapVehicle(obj: ChannelItem, overlap: any[], color: string) {
      const overlapItem = overlap.filter((phase: any) => phase.id === obj.controlsource)[0];
      if (!overlapItem?.direction) {
        this.realdirOptions.set(obj.id, []);
        return;
      }
      const directions = overlapItem.direction || overlapItem.peddirection;
      this.realdirOptions.set(obj.id, directions.map((item: number) => ({
        value: item,
        item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
        label: overlapItem.direction ? this.getPhaseDesc([item]) : this.getPedPhaseDesc([item])
      })));
    },

    /**
     * 处理车道灯
     */
    processLaneLights(obj: ChannelItem, color: string) {
      const status = generatePhaseStatusOptions();
      obj.realtype = 'direction';
      this.realdirOptions.set(obj.id, status);
    },

    /**
     * 处理相位机动车
     */
    processPhaseVehicle(obj: ChannelItem, phaseList: any[], color: string) {
      const phaseItem = phaseList.filter((phase: any) => phase.id === obj.controlsource)[0];
      if (phaseItem?.direction) {
        this.realdirOptions.set(obj.id, phaseItem.direction.map((item: number) => ({
          value: item,
          item: [{ id: item, peddirection: [], color: color }],
          label: this.getPhaseDesc([item])
        })));
      }
    },

    /**
     * 处理跟随相位行人
     */
    processOverlapPedestrian(obj: ChannelItem, overlap: any[], color: string) {
      const overlapItem = overlap.filter((phase: any) => phase.id === obj.controlsource)[0];
      if (!overlapItem?.peddirection) {
        this.realdirOptions.set(obj.id, []);
        return;
      }
      const directions = overlapItem.direction || overlapItem.peddirection;
      this.realdirOptions.set(obj.id, directions.map((item: number) => ({
        value: item,
        item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
        label: overlapItem.direction ? this.getPhaseDesc([item]) : this.getPedPhaseDesc([item])
      })));
    },

    /**
     * 处理相位行人
     */
    processPhasePedestrian(obj: ChannelItem, phaseList: any[], color: string) {
      const phaseItem = phaseList.filter((phase: any) => phase.id === obj.controlsource)[0];
      if (phaseItem?.peddirection) {
        this.realdirOptions.set(obj.id, phaseItem.peddirection.map((item: number) => ({
          value: item,
          item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
          label: this.getPedPhaseDesc([item])
        })));
      }
    },

    // ==================== 事件处理方法 ====================

    /**
     * 处理变化
     */
    handleChange(value: number[], index: number, val: ChannelItem) {
      const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
      const channel = this.globalParamModel.getParamsByType('channelList');
      const overlap = this.globalParamModel.getParamsByType('overlaplList');

      channel[index].controltype = value[0];
      if (value[1]) {
        channel[index].controlsource = value[1];
      }
      this.createCurrentDescMap();

      // 根据控制类型设置相应的配置
      this.handleControlTypeChange(value, index, val, channel, overlap, color);
    },

    /**
     * 处理控制类型变化
     */
    handleControlTypeChange(value: number[], index: number, val: ChannelItem, channel: any[], overlap: any[], color: string) {
      if (value[0] === 2) {
        // 机动车相位
        this.setupVehiclePhase(index, val, channel, color);
      } else if (value[0] === 3) {
        // 行人相位
        this.setupPedestrianPhase(index, val, channel, color);
      } else if (value[0] === 4) {
        // 跟随相位
        this.setupOverlapPhase(index, val, channel, overlap, color);
      } else if (value[0] === 5) {
        // 行人跟随相位
        this.setupOverlapPedestrianPhase(index, val, channel, overlap, color);
      } else {
        // 其他类型
        this.setupOtherType(index, val, channel);
      }
    },

    /**
     * 设置机动车相位
     */
    setupVehiclePhase(index: number, val: ChannelItem, channel: any[], color: string) {
      channel[index].type = 0;
      channel[index].realdir = [];
      this.turnOptions.set(val.id, this.carList);
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const phaseItem = phaseList.filter((phase: any) => phase.id === channel[index].controlsource)[0];
      if (phaseItem?.direction) {
        this.realdirOptions.set(val.id, phaseItem.direction.map((item: number) => ({
          value: item,
          item: [{ id: item, peddirection: [], color: color }],
          label: this.getPhaseDesc([item])
        })));
      }
    },

    /**
     * 设置行人相位
     */
    setupPedestrianPhase(index: number, val: ChannelItem, channel: any[], color: string) {
      channel[index].type = 2;
      channel[index].realdir = [];
      this.turnOptions.set(val.id, this.peopleList);
      const phaseList = this.globalParamModel.getParamsByType('phaseList');
      const phaseItem = phaseList.filter((phase: any) => phase.id === channel[index].controlsource)[0];
      if (phaseItem?.peddirection) {
        this.realdirOptions.set(val.id, phaseItem.peddirection.map((item: number) => ({
          value: item,
          item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
          label: this.getPedPhaseDesc([item])
        })));
      }
    },

    /**
     * 设置跟随相位
     */
    setupOverlapPhase(index: number, val: ChannelItem, channel: any[], overlap: any[], color: string) {
      channel[index].type = 0;
      channel[index].realdir = [];
      this.turnOptions.set(val.id, this.carList);
      const overlapItem = overlap.filter((phase: any) => phase.id === channel[index].controlsource)[0];
      if (overlapItem?.direction) {
        this.realdirOptions.set(val.id, overlapItem.direction.map((item: number) => ({
          value: item,
          item: [{ id: item, peddirection: [], color: color }],
          label: this.getPhaseDesc([item])
        })));
      } else {
        this.realdirOptions.set(val.id, []);
      }
    },

    /**
     * 设置行人跟随相位
     */
    setupOverlapPedestrianPhase(index: number, val: ChannelItem, channel: any[], overlap: any[], color: string) {
      channel[index].type = 2;
      channel[index].realdir = [];
      this.turnOptions.set(val.id, this.peopleList);
      const overlapItem = overlap.filter((phase: any) => phase.id === channel[index].controlsource)[0];
      if (overlapItem?.peddirection) {
        this.realdirOptions.set(val.id, overlapItem.peddirection.map((item: number) => ({
          value: item,
          item: [{ id: 0, peddirection: getPed(item, this.sidewalkPhaseData), color: color }],
          label: this.getPedPhaseDesc([item])
        })));
      } else {
        this.realdirOptions.set(val.id, []);
      }
    },

    /**
     * 设置其他类型
     */
    setupOtherType(index: number, val: ChannelItem, channel: any[]) {
      channel[index].type = 4;
      channel[index].realdir = [];
      this.turnOptions.set(val.id, []);
      this.realdirOptions.set(val.id, []);
    },

    /**
     * 创建当前描述映射（优化版本）
     */
    createCurrentDescMap() {
      const channels = this.globalParamModel.getParamsByType('channelList');
      const descMap = new Map<number, string[]>();

      // 优化：预构建类型选项映射，避免重复查找
      const typeOptionsMap = new Map<number, any>();
      const childrenOptionsMap = new Map<string, any>();

      this.typeOptions.forEach(type => {
        typeOptionsMap.set(type.value, type);
        if (type.children && type.children.length) {
          type.children.forEach(child => {
            childrenOptionsMap.set(`${type.value}_${child.value}`, child);
          });
        }
      });

      // 遍历通道数据，构建描述映射
      for (const ele of channels) {
        if (!ele.typeAndSouce || ele.typeAndSouce.length === 0) continue;

        const desc: string[] = [];

        // 获取主类型描述
        if (ele.typeAndSouce[0] !== undefined) {
          const typeOption = typeOptionsMap.get(ele.typeAndSouce[0]);
          desc[0] = typeOption ? typeOption.label : '';
        }

        // 获取子类型描述
        if (ele.typeAndSouce[1] !== undefined) {
          const childKey = `${ele.typeAndSouce[0]}_${ele.typeAndSouce[1]}`;
          const childOption = childrenOptionsMap.get(childKey);
          desc[1] = childOption ? childOption.label : '';
        }

        descMap.set(ele.id, desc);
      }

      // 更新本地和全局描述映射
      this.desclist = descMap;
      this.globalStore.SetChannelDesc(descMap);
    },

    /**
     * 增加ID
     */
    increaseId() {
      const channelList = this.globalParamModel.getParamsByType('channelList');
      this.id = increaseId(channelList);
    },

    // ==================== CRUD 操作方法 ====================

    /**
     * 编辑处理
     */
    handleEdit(index: number, row: ChannelItem) {
      // 编辑逻辑
    },

    /**
     * 删除处理（优化版本）
     */
    handleDelete(index: number, row: ChannelItem) {
      ElMessageBox.confirm(
        '确定删除此通道吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {

        // 2. 删除通道数据
        this.globalParamModel.deleteParamsByType('channelList', index, 1);

        // 3. 清理相关映射数据
        this.desclist.delete(row.id);
        this.turnOptions.delete(row.id);
        this.realdirOptions.delete(row.id);

        // 4. 更新全局描述映射
        this.globalStore.SetChannelDesc(this.desclist);

        // 5. 手动触发相关组件更新
        this.$nextTick(() => {
          refreshChannelLockDescData();
          refreshControlPanelDescData();
        });

        ElMessage({
          type: 'success',
          message: '删除成功'
        });
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    /**
     * 删除所有数据（优化版本）
     */
    deleteAllData() {
      ElMessageBox.confirm(
        '确定删除所有通道数据吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 1. 设置手动操作标志
        this._isManualAdd = true;

        // 2. 删除所有通道数据
        const channelList = this.globalParamModel.getParamsByType('channelList');
        this.globalParamModel.deleteParamsByType('channelList', 0, channelList.length);

        // 3. 重置ID计数器
        this.id = 1;

        // 4. 清空相关映射数据
        this.desclist.clear();
        this.turnOptions.clear();
        this.realdirOptions.clear();

        // 5. 更新全局描述映射
        this.globalStore.SetChannelDesc(new Map());

        // 6. 手动触发相关组件更新
        this.$nextTick(() => {
          refreshChannelLockDescData();
          refreshControlPanelDescData();
        });

        ElMessage({
          type: 'success',
          message: '删除成功'
        });
      }).catch(() => {
        ElMessage({
          type: 'info',
          message: '已取消删除'
        });
      });
    },

    /**
     * 克隆处理
     */
    handleClone(index: number, row: ChannelItem) {
      // 1. 验证前置条件
      if (this.globalParamModel.getParamLength('phaseList') === 0) {
        ElMessage.error('请先创建相位！');
        return;
      }

      if (this.globalParamModel.getParamLength('channelList') >= 40) {
        ElMessage.error('最多只能有40个通道数据！');
        return;
      }

      // 2. 设置手动添加标志，避免watch重复处理
      this._isManualAdd = true;

      // 3. 生成新的通道ID
      this.increaseId();

      // 4. 创建克隆数据
      const data: ChannelItem = {};
      Object.assign(data, row);
      data.id = this.id;

      // 5. 添加数据到store
      this.globalParamModel.addParamsByType('channelList', data);

      // 6. 立即排序通道数组
      const channelList = this.globalParamModel.getParamsByType('channelList');
      channelList.sort(compareProperty('id'));

      // 7. 手动触发后续处理
      this.$nextTick(() => {
        this.createCurrentDescMap();
        refreshChannelLockDescData();
        refreshControlPanelDescData();
      });
    },

    /**
     * 添加新通道
     */
    onAdd() {
      // 1. 验证前置条件
      if (this.globalParamModel.getParamLength('phaseList') === 0) {
        ElMessage.error('请先创建相位！');
        return;
      }

      if (this.globalParamModel.getParamLength('channelList') >= 40) {
        ElMessage.error('最多只能有40个通道数据！');
        return;
      }

      // 2. 生成新的通道ID
      this.increaseId();

      // 3. 创建新通道数据
      const channelInitData = {
        id: this.id,
        ...defaultChannelInitData
      };

      // 4. 添加数据到store（直接操作响应式数据）
      this.globalParamModel.addParamsByType('channelList', channelInitData);

      // 5. 立即排序通道数组（确保数据按ID排序）
      const channelList = this.globalParamModel.getParamsByType('channelList');
      channelList.sort(compareProperty('id'));

      // 6. 手动触发必要的更新
      this.$nextTick(() => {
        this.processChannelOptionsData();
        this.createCurrentDescMap();
      });
    },

    // ==================== 对话框方法 ====================

    /**
     * 打开接线图
     */
    openWiringDiagram() {
      this.diagramVisible = true;
    },

    /**
     * 关闭接线图
     */
    closeWiringDiagram() {
      this.diagramVisible = false;
    },

    // ==================== 辅助方法 ====================

    /**
     * 获取相位描述
     */
    getPhaseDesc(list: number[]): string {
      return getPhaseDesc(list);
    },

    /**
     * 获取行人相位描述
     */
    getPedPhaseDesc(list: number[]): string {
      return getPedPhaseDesc(list);
    }
  }
});
</script>

<style scoped>
/* * {
   margin: 0;
   padding: 0} */
body {
  overflow: auto;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
}
.tb-edit .el-input {
  display: none;
}
.tb-edit .current-row .el-input {
  display: block;
}
.tb-edit .current-row .el-input + span {
  display: none;
}
.tb-edit .el-select {
  display: none;
}
.tb-edit .el-cascader {
  display: none;
}
.tb-edit .current-row .el-select {
  display: block;
}
.tb-edit .current-row .el-select + span {
  display: none;
}
.tb-edit .current-row .el-cascader {
  display: block;
}
.tb-edit .current-row .el-cascader + span {
  display: none;
}
.tb-edit .el-input-number {
  display: none;
}
.tb-edit .current-row .el-input-number {
  display: block;
}
.tb-edit .current-row .el-input-number + span {
  display: none;
}
.tb-edit .el-col {
  display: none;
}
.tb-edit .current-row .el-col {
  display: block;
}
.tb-edit .current-row .el-col + span {
  display: none;
}
.tb-edit .el-popover {
  display: none;
}
.tb-edit .current-row .el-popover {
  display: block;
}
.tb-edit .current-row .el-popover + span {
  display: none;
}
.showSpan {
  display: block;
}
.tb-edit .current-row .showSpan {
  display: none;
}
/* .el-cascader {
  display: inline-block;
  position: relative;
  font-size: 14px;
  line-height: 40px;
  width: 100%;
} */
.config {
  width: 100%;
}
</style>
<style lang="scss">
.WiringDiagram {
  .el-dialog__body {
    padding: 30PX;
  }
}
</style>
