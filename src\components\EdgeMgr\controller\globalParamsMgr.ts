// 该类是对全局对象tscParam操作的管理
import EdgeModelBase from '@/components/EdgeMgr/EdgeModelBase'
import { useGlobalParamModelStore } from '@/store/modules/globalParamModel'
import { useGlobalStore } from '@/store/modules/globalParam'
import { storeToRefs } from 'pinia'

interface GlobalStore {
  tscParam: any
}
export default class GlobalParamsMgr extends EdgeModelBase {
  GlobalParamModelStore: any
  GlobalStore: any
  constructor() {
    super('GlobalParamsMgr')
    this.GlobalParamModelStore = useGlobalParamModelStore()
    this.GlobalStore = useGlobalStore()
  }

  Init(): void {
    this.GlobalParamModelStore.InitGlobalParamModel(this)
    // store.dispatch('InitGlobalParamModel', this)
  }

  setGlobalParams(globalData: any): void {
    // 设置全局变量，操作store
    this.GlobalStore.SaveTscParam(globalData)
    // store.dispatch('SaveTscParam', globalData)
  }

  setSingleParam(param: { type: string; data: any }): void {
    this.GlobalStore.SaveSingleParam(param)
    // store.dispatch('SaveSingleParam', param)
  }

  getGlobalParams(): any {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    return tscParam.value
    // return store.getters.tscParam
  }

  getParamsByType(key: string): any[] {
    // 获取全局对象的某个字段的数据
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    return tscParam.value[key] || []
    // return store.getters.tscParam.value[key]
  }

  getParamLength(key: string): number {
    // 获取全局对象的某个字段数据的长度
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    return tscParam.value[key] ? tscParam.value[key].length : 0
    // return store.getters.tscParam.value[key].length
  }

  addParamsByType(key: string, payload: any): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    if (!tscParam.value[key]) {
      tscParam.value[key] = []
    }
    tscParam.value[key].push(payload)
    // store.getters.tscParam.value[key].push(payload)
  }

  addParamsChannelGreen(key: string, payload: any): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    tscParam.value[key] = payload
    // store.getters.tscParam.value[key] = payload
  }

  deleteParamsByType(key: string, index: number, num: number): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    if (tscParam.value[key]) {
      tscParam.value[key].splice(index, num)
    }
    // store.getters.tscParam.value[key].splice(index, num)
  }

  deleteParams(key: string): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    tscParam.value[key] = []
    // store.getters.tscParam.value[key] = []
  }

  reset(): void {
    this.GlobalStore.ResetTscParam()
    // store.dispatch('ResetTscParam')
  }

  deleteChildrenParamsById(key: string, id: any, fieldchild: string): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    if (tscParam.value[key]) {
      tscParam.value[key].forEach((ele: any) => {
        if (ele[fieldchild]) {
          for (let i = ele[fieldchild].length - 1; i >= 0; i--) {
            if (ele[fieldchild][i].channelid === id) {
              ele[fieldchild].splice(i, 1)
            }
          }
        }
      })
    }
    // store.getters.tscParam.value[key].forEach(ele => {
    //   ele[fieldchild].forEach((child, index) => {
    //     if (child.channelid === id) {
    //       ele[fieldchild].splice(index, 1)
    //     }
    //   })
    // })
  }

  deleteAllChildrenParams(key: string, fieldchild: string): void {
    const { tscParam } = storeToRefs(this.GlobalStore) as GlobalStore
    if (tscParam.value[key]) {
      tscParam.value[key].forEach((ele: any) => {
        ele[fieldchild] = []
      })
    }
    // store.getters.tscParam.value[key].forEach(ele => {
    //   ele[fieldchild] = []
    // })
  }
}
