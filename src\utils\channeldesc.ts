import { getPhaseDesc } from '@/utils/phasedesc'
import { getPedPhaseDesc } from '@/utils/pedphasedesc'
import { getGlobalParamsMgr } from '@/components/EdgeMgr/globalManager'
import { useGlobalStore } from '@/store/modules/globalParam'

// 类型定义
interface ControlSource {
  value: number;
  label: string;
}

interface PhaseItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
  controltype?: number;
}

interface OverlapItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
}

interface ChannelLockInfo {
  channelid: number;
  desc: string;
  lockstatus: number;
}

interface ChannelLockData {
  channellocKinfo: ChannelLockInfo[];
}

interface ChannelInfo {
  channelid: number;
  desc: string;
  channelstatus: number;
}

interface ManualPanelData {
  channel: ChannelInfo[];
}

interface ManualPanel {
  keyconfig: ManualPanelData[];
}

interface TypeOption {
  value: number;
  label: string;
}

// 比较函数
const compareProperty = <T>(property: keyof T) => {
  return function (a: T, b: T): number {
    const value1 = a[property] as number;
    const value2 = b[property] as number;
    return value1 - value2;
  };
};

/**
 * 生成相位描述
 * @param phases 可选的相位列表
 * @returns 控制源数组
 */
export function getControSource(phases?: PhaseItem[]): ControlSource[] {
  const controlSources: ControlSource[] = [];
  let phaseList: PhaseItem[] = getGlobalParamsMgr().getParamsByType('phaseList');
  if (phaseList === undefined && phases && phases.length) {
    phaseList = phases;
  }
  for (let i = 0; i < phaseList.length; i++) {
    if (phaseList[i].controltype !== 99) {
      const pattern: ControlSource = {} as ControlSource;
      const patternNum: number = phaseList[i].id;
      let patternDescription: string | number;
      const phaseDesc: string = phaseList[i].direction ? getPhaseDesc(phaseList[i].direction) : '';
      const pedphaseDesc: string = phaseList[i].peddirection ? getPedPhaseDesc(phaseList[i].peddirection) : '';
      if (phaseDesc === '' && pedphaseDesc === '') {
        patternDescription = patternNum;
      } else {
        if (phaseDesc !== '' && pedphaseDesc !== '') {
          patternDescription = patternNum + '-' + phaseDesc + '--' + pedphaseDesc;
        } else if (phaseDesc === '') {
          patternDescription = patternNum + '-' + pedphaseDesc;
        } else {
          patternDescription = patternNum + '-' + phaseDesc;
        }
      }
      pattern.value = patternNum;
      pattern.label = patternDescription.toString();
      controlSources.push(pattern);
    }
  }
  return controlSources;
}

/**
 * 生成相位描述（虚相位也显示）
 * @param phases 可选的相位列表
 * @returns 控制源数组
 */
export function getAllControSource(phases?: PhaseItem[]): ControlSource[] {
  const controlSources: ControlSource[] = [];
  let phaseList: PhaseItem[] = [];

  if (phases && phases.length) {
    phaseList = phases;
  } else {
    phaseList = getGlobalParamsMgr().getParamsByType('phaseList');
  }
  for (let i = 0; i < phaseList.length; i++) {
    const pattern: ControlSource = {} as ControlSource;
    const patternNum: number = phaseList[i].id;
    let patternDescription: string | number;
    const phaseDesc: string = phaseList[i].direction ? getPhaseDesc(phaseList[i].direction) : '';
    const pedphaseDesc: string = phaseList[i].peddirection ? getPedPhaseDesc(phaseList[i].peddirection) : '';
    if (phaseDesc === '' && pedphaseDesc === '') {
      patternDescription = patternNum;
    } else {
      if (phaseDesc !== '' && pedphaseDesc !== '') {
        patternDescription = patternNum + '-' + phaseDesc + '--' + pedphaseDesc;
      } else if (phaseDesc === '') {
        patternDescription = patternNum + '-' + pedphaseDesc;
      } else {
        patternDescription = patternNum + '-' + phaseDesc;
      }
    }
    pattern.value = patternNum;
    pattern.label = patternDescription.toString();
    controlSources.push(pattern);
  }
  return controlSources;
}

/**
 * 生成跟随相位描述
 * @param overlaps 可选的跟随相位列表
 * @returns 控制类型数组
 */
export function getOverLap(overlaps?: OverlapItem[]): ControlSource[] | undefined {
  const controlTypes: ControlSource[] = [];
  let overlaplList: OverlapItem[] = [];

  if (overlaps && overlaps.length) {
    overlaplList = overlaps;
  } else {
    overlaplList = getGlobalParamsMgr().getParamsByType('overlaplList');
    if (!overlaplList) return;
  }
  for (let i = 0; i < overlaplList.length; i++) {
    const overlap: ControlSource = {} as ControlSource;
    const overlapNum: number = overlaplList[i].id;
    let overlapDescription: string | number;
    const phaseDesc: string = overlaplList[i].direction ? getPhaseDesc(overlaplList[i].direction) : '';
    const pedphaseDesc: string = overlaplList[i].peddirection ? getPedPhaseDesc(overlaplList[i].peddirection) : '';
    if (phaseDesc === '' && pedphaseDesc === '') {
      overlapDescription = overlapNum;
    } else {
      if (phaseDesc !== '' && pedphaseDesc !== '') {
        overlapDescription = overlapNum + '-' + phaseDesc + '--' + pedphaseDesc;
      } else if (phaseDesc === '') {
        overlapDescription = overlapNum + '-' + pedphaseDesc;
      } else {
        overlapDescription = overlapNum + '-' + phaseDesc;
      }
    }
    overlap.value = overlapNum;
    overlap.label = overlapDescription.toString();
    controlTypes.push(overlap);
  }
  return controlTypes;
}


/**
 * 根据通道描述，更新已配置通道锁定描述
 */
export function refreshChannelLockDescData(): void {
  const channellock: ChannelLockData[] = getGlobalParamsMgr().getParamsByType('channellock');
  const globalStore = useGlobalStore();
  const channelDescMap: Map<number, string> = globalStore.channelDescMap;
  if (channellock === undefined) return;
  channellock.forEach(lockdata => {
    const locklength: number = lockdata.channellocKinfo.length;
    const channellength: number = channelDescMap.size;
    if (locklength <= channellength) {
      for (const [key, value] of channelDescMap.entries()) {
        const ids: number[] = lockdata.channellocKinfo.map(ele => ele.channelid);
        if (ids.indexOf(key) !== -1) {
          lockdata.channellocKinfo.forEach(info => {
            if (info.channelid === key) {
              info.desc = value;
            }
          });
        } else {
          const obj: ChannelLockInfo = {
            channelid: key,
            desc: value,
            lockstatus: 0
          };
          lockdata.channellocKinfo.push(obj);
        }
      }
    } else {
      if (channellength === 0) {
        // 全部通道删除
        lockdata.channellocKinfo = [];
      } else {
        // 部分通道删除
        lockdata.channellocKinfo.forEach((info, index) => {
          if (channelDescMap.get(info.channelid) === undefined) {
            lockdata.channellocKinfo.splice(index, 1);
          }
        });
      }
    }
    // 根据通道id重新排序
    lockdata.channellocKinfo.sort(compareProperty<ChannelLockInfo>('channelid'));
  });
}

/**
 * 根据通道描述，更新已配置手动面板描述
 */
export function refreshControlPanelDescData(): void {
  const globalParamsMgr = getGlobalParamsMgr();
  const manualPanelData: ManualPanel = globalParamsMgr.getParamsByType('manualpanel');
  console.log(manualPanelData);
  const manualpanel: ManualPanelData[] = manualPanelData.keyconfig;
  const globalStore = useGlobalStore();
  const channelDescMap: Map<number, string> = globalStore.channelDescMap;
  if (manualpanel === undefined) return;
  manualpanel.forEach(manualdata => {
    const manuallength: number = manualdata.channel.length;
    const channellength: number = channelDescMap.size;
    if (manuallength <= channellength) {
      // 通道增加或上载
      for (const [key, value] of channelDescMap.entries()) {
        const ids: number[] = manualdata.channel.map(ele => ele.channelid);
        if (ids.indexOf(key) !== -1) {
          manualdata.channel.forEach(info => {
            if (info.channelid === key) {
              info.desc = value;
            }
          });
        } else {
          const obj: ChannelInfo = {
            channelid: key,
            desc: value,
            channelstatus: 1
          };
          manualdata.channel.push(obj);
        }
      }
    } else {
      if (channellength === 0) {
        // 全部通道删除
        manualdata.channel = [];
      } else {
        // 部分通道删除
        manualdata.channel.forEach((info, index) => {
          if (channelDescMap.get(info.channelid) === undefined) {
            manualdata.channel.splice(index, 1);
          }
        });
      }
    }
    // 根据通道id重新排序
    manualdata.channel.sort(compareProperty<ChannelInfo>('channelid'));
  });
  console.log(manualpanel);
}

export function getTypeOptions () {
  return [{
    value: 2,
    label: '机动车相位'
  }, {
    value: 3,
    label: '行人相位'
  }, {
    value: 4,
    label: '跟随相位'
  }, {
    value: 5,
    label: '行人跟随相位'
  }, {
    value: 6,
    label: '车道灯'
  }, {
    value: 0,
    label: '不启用'
  }]
}
