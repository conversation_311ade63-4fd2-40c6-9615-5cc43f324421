import type { PhasePosition, PhaseJson, RoadDirection } from './types';

export default class PhaseDataModel {
  private PhasePosMap: Map<string | number, PhasePosition>;
  private SidePosMap: Map<string | number, PhasePosition>;
  private RampMainPosMap: Map<string | number, PhasePosition>;
  private RampSidePosMap: Map<string | number, PhasePosition>;
  private BusMapMap: Map<string | number, PhasePosition>;
  private BusPhaseMap: Map<string | number, PhasePosition>;
  private EffectPosMap: Map<string | number, PhasePosition>;
  private roadDirection: RoadDirection;

  constructor(roadDirection: RoadDirection = 'right') {
    this.PhasePosMap = new Map();
    this.SidePosMap = new Map();
    this.RampMainPosMap = new Map(); // 匝道主路坐标
    this.RampSidePosMap = new Map(); // 匝道支路坐标
    this.BusMapMap = new Map(); // 公交相位底图坐标
    this.BusPhaseMap = new Map(); // 公交相位坐标
    this.EffectPosMap = new Map(); // 有效方向坐标
    this.roadDirection = roadDirection;
    this.Init();
  }

  private Init(): void {
    try {
      const phaseJsonData = require('./posJson/phasePos.json');
      const sideJsonData = require('./posJson/sidePos.json');
      const rampJsonData = require('./posJson/rampPos.json');
      const busJsonData = require('./posJson/busPos.json');
      const effectJsonData = require('./posJson/effectPos.json');

      let phaseJson = phaseJsonData.phaseList;
      let sideJson = sideJsonData.sideList;
      let busJson = busJsonData;
      let effectJson = effectJsonData.effectDirectionList;

      if (this.roadDirection === 'left') {
        const phaseLeftData = require('./posJson/phasePos.left.json');
        const busLeftData = require('./posJson/busPos.left.json');
        phaseJson = phaseLeftData.phaseList;
        busJson = busLeftData;
      }

      // console.log('PhaseDataModel Init - checking data types:');
      // console.log('phaseJson:', Array.isArray(phaseJson), phaseJson?.length);
      // console.log('sideJson:', Array.isArray(sideJson), sideJson?.length);
      // console.log('rampMainPhaseList:', Array.isArray(rampJsonData.rampMainPhaseList), rampJsonData.rampMainPhaseList?.length);
      // console.log('rampSidePhaseList:', Array.isArray(rampJsonData.rampSidePhaseList), rampJsonData.rampSidePhaseList?.length);
      // console.log('busMap:', Array.isArray(busJson.busMap), busJson.busMap?.length);
      // console.log('busphases:', Array.isArray(busJson.busphases), busJson.busphases?.length);
      // console.log('effectJson:', Array.isArray(effectJson), effectJson?.length);

      this.handlePos(phaseJson, this.PhasePosMap, 'phaseList');
      this.handlePos(sideJson, this.SidePosMap, 'sideList');
      this.handlePos(rampJsonData.rampMainPhaseList, this.RampMainPosMap, 'rampMainPhaseList');
      this.handlePos(rampJsonData.rampSidePhaseList, this.RampSidePosMap, 'rampSidePhaseList');
      this.handlePos(busJson.busMap, this.BusMapMap, 'busMap');
      this.handlePos(busJson.busphases, this.BusPhaseMap, 'busphases');
      this.handlePos(effectJson, this.EffectPosMap, 'effectDirectionList');
    } catch (error) {
      console.error('PhaseDataModel initialization failed:', error);
    }
  }

  private handlePos(phaseJson: PhaseJson[] | undefined, phaseMap: Map<string | number, PhasePosition>, dataSource?: string): void {
    const source = dataSource || 'unknown';

    if (!phaseJson) {
      console.warn(`PhaseDataModel [${source}]: phaseJson is undefined`);
      return;
    }

    if (!Array.isArray(phaseJson)) {
      console.warn(`PhaseDataModel [${source}]: phaseJson is not an array, received:`, typeof phaseJson, phaseJson);
      return;
    }

    phaseJson.forEach((phase, index) => {
      try {
        if (!phase || typeof phase !== 'object') {
          console.warn(`PhaseDataModel [${source}]: Invalid phase data at index ${index}:`, phase);
          return;
        }

        const value: PhasePosition = {
          name: phase.name,
          x: phase.x,
          y: phase.y
        };

        if (phase.ename) {
          value.ename = phase.ename;
        }

        if (phase.desc) {
          value.desc = phase.desc;
        }

        phaseMap.set(phase.id, value);
      } catch (error) {
        console.error(`PhaseDataModel [${source}]: Error processing phase at index ${index}:`, error, phase);
      }
    });
  }

  getPhase(id: string | number): PhasePosition | undefined {
    return this.PhasePosMap.get(id);
  }

  getSidePos(id: string | number): PhasePosition | undefined {
    return this.SidePosMap.get(id);
  }

  getMainPhasePos(id: string | number): PhasePosition | undefined {
    return this.RampMainPosMap.get(id);
  }

  getSidePhasePos(id: string | number): PhasePosition | undefined {
    return this.RampSidePosMap.get(id);
  }

  getBusMapPos(id: string | number): PhasePosition | undefined {
    return this.BusMapMap.get(id);
  }

  getBusPhasePos(id: string | number): PhasePosition | undefined {
    return this.BusPhaseMap.get(id);
  }

  getEffectPos(id: string | number): PhasePosition | undefined {
    return this.EffectPosMap.get(id);
  }
}
