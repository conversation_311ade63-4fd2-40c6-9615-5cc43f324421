# PhaseDataModel

相位数据模型，用于管理交通信号相位的位置和配置信息。

## 功能

- 支持左行和右行车道方向配置
- 管理车辆相位、行人相位、匝道相位、公交相位等多种相位类型
- 提供统一的相位数据访问接口

## 使用方法

### 基本用法

```typescript
import PhaseDataModel from '@/components/PhaseDataModel';

// 创建右行车道方向的相位数据模型（默认）
const rightModel = new PhaseDataModel('right');

// 创建左行车道方向的相位数据模型
const leftModel = new PhaseDataModel('left');
```

### API 方法

#### getPhase(id: string | number)
获取车辆相位信息
```typescript
const phase = model.getPhase(1); // 获取ID为1的车辆相位
```

#### getSidePos(id: string | number)
获取行人相位信息
```typescript
const sidePos = model.getSidePos(1); // 获取ID为1的行人相位
```

#### getMainPhasePos(id: string | number)
获取匝道主路相位信息

#### getSidePhasePos(id: string | number)
获取匝道支路相位信息

#### getBusMapPos(id: string | number)
获取公交相位底图坐标

#### getBusPhasePos(id: string | number)
获取公交相位坐标

#### getEffectPos(id: string | number)
获取有效方向坐标

### 返回数据格式

```typescript
interface PhasePosition {
  name: string;      // 相位名称，如"东直行"
  x: number;         // X坐标
  y: number;         // Y坐标
  ename?: string;    // 英文名称，如"East-Straight"
  desc?: string;     // 描述信息
}
```

## 数据文件

相位数据存储在 `src/posJson/` 目录下：

- `phasePos.json` - 右行车道车辆相位数据
- `phasePos.left.json` - 左行车道车辆相位数据
- `sidePos.json` - 行人相位数据
- `rampPos.json` - 匝道相位数据
- `busPos.json` / `busPos.left.json` - 公交相位数据
- `effectPos.json` - 有效方向数据

## 使用示例

```typescript
// 在组件中使用
import PhaseDataModel from '@/components/PhaseDataModel';

export default {
  setup() {
    const phaseModel = new PhaseDataModel('right');
    
    // 获取行人相位名称
    const getPedestrianName = (id: number) => {
      const pos = phaseModel.getSidePos(id);
      return pos?.name || '';
    };
    
    return {
      getPedestrianName
    };
  }
};
```
