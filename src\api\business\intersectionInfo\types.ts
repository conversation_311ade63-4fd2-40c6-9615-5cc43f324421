export interface IntersectionInfoVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 路口编码
   */
  intersectionCode: string;

  /**
   * 路口名称
   */
  intersectionName: string;

  /**
 * 路口类型
 */
  intersectionType: string;

  /**
   * 路口描述
   */
  intersectionDesc: string;

  /**
   * 地图经度
   */
  longitude: string;

  /**
   * 地图纬度
   */
  latitude: string;

  /**
   * 路口信号机id
   */
  intersectionSignalId: string | number;

  /**
   * 路口配置
   */
  intersectionConfig: string;

}

export interface IntersectionInfoForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 路口编码
   */
  intersectionCode?: string;

  /**
   * 路口名称
   */
  intersectionName?: string;


  /**
   * 路口类型
   */
  intersectionType?: string;

  /**
   * 路口描述
   */
  intersectionDesc?: string;

  /**
   * 地图经度
   */
  longitude?: string;

  /**
   * 地图纬度
   */
  latitude?: string;

  /**
   * 路口信号机id
   */
  intersectionSignalId?: string | number;

  /**
   * 路口配置
   */
  intersectionConfig?: string;

}

export interface IntersectionInfoQuery extends PageQuery {

  /**
   * 路口编码
   */
  intersectionCode?: string;

  /**
   * 路口名称
   */
  intersectionName?: string;

  /**
 * 路口类型
 */
  intersectionType?: string;

  /**
   * 路口信号机id
   */
  intersectionSignalId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}



