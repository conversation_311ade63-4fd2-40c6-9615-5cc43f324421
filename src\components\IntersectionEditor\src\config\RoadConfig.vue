<template>
  <div class="road-config">
    <el-form label-position="left" label-width="120px" :model="attributes">
      <el-form-item label="路段名称">
        <el-input v-model="attributes.roadName" placeholder="请输入路段名称" @change="handleChange" />
      </el-form-item>

      <el-form-item label="进口车道数">
        <el-input-number 
          v-model="attributes.entryLanes" 
          :min="1" 
          :max="6" 
          @change="handleChange" 
        />
      </el-form-item>

      <el-form-item label="中央分隔带类型">
        <el-select v-model="attributes.dividerType" @change="handleChange">
          <el-option label="无分隔带" value="0" />
          <el-option label="实线分隔带" value="1" />
          <el-option label="双实线分隔带" value="2" />
          <el-option label="绿化分隔带" value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="出口车道数">
        <el-input-number 
          v-model="attributes.exitLanes" 
          :min="1" 
          :max="6" 
          @change="handleChange" 
        />
      </el-form-item>

      <el-form-item label="掉头方式">
        <el-select v-model="attributes.advanceTurn" @change="handleChange">
          <el-option label="无掉头" value="0" />
          <el-option label="普通掉头" value="1" />
          <el-option label="提前掉头" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="其他设置">
        <el-checkbox 
          v-model="attributes.hasLeftTurnWaiting" 
          label="左转待转区" 
          @change="handleChange" 
        />
        <el-checkbox 
          v-model="attributes.straightWaiting" 
          label="直行待行区" 
          @change="handleChange" 
        />
        <el-checkbox 
          v-model="attributes.hasPedestrianCrossing" 
          label="人行横道" 
          @change="handleChange" 
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits } from 'vue';
import { RoadAttributes } from '../types';

// 定义属性
const props = defineProps<{
  initialAttributes: RoadAttributes;
  roadId: number | null;
}>();

// 定义事件
const emit = defineEmits<{
  (e: 'change', data: { roadId: number, attributes: RoadAttributes }): void;
}>();

// 道路属性
const attributes = reactive<RoadAttributes>({
  roadName: '',
  entryLanes: 3,
  dividerType: '2',
  exitLanes: 3,
  advanceTurn: '0',
  hasLeftTurnWaiting: false,
  straightWaiting: false,
  hasPedestrianCrossing: true
});

// 监听初始属性变化
watch(() => props.initialAttributes, (newAttrs) => {
  if (newAttrs) {
    Object.assign(attributes, newAttrs);
  }
}, { immediate: true, deep: true });

// 处理属性变更
const handleChange = (): void => {
  if (props.roadId) {
    emit('change', {
      roadId: props.roadId,
      attributes: { ...attributes }
    });
  }
};
</script>

<style scoped>
.road-config {
  padding: 10px;
}
</style>