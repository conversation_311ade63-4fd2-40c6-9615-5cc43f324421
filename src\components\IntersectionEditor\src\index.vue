<template>
  <div class="intersection-editor" :class="{ 'fullscreen': isFullscreen }">
    <!-- 错误提示 -->
    <div v-if="configError" class="config-error-message">
      <el-alert
        title="配置格式错误"
        type="error"
        description="传入的路口配置格式不正确，请检查配置数据格式。"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 左侧画布区域 -->
    <div class="canvas-area">
      <div class="canvas-container">
        <!-- 画布组件 -->
        <div class="canvas-wrapper">
          <div :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
            <canvas class="lane" ref="canvasRef"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧配置面板 -->
    <div v-if="!hideConfig" class="config-panel">
      <!-- 上部分：类型选择器区域 -->
      <div class="panel-section type-selector-section">
        <TypeSelector
          v-model="intersectionType"
          @update:modelValue="handleTypeChange"
        />
      </div>

      <!-- 中部分：路段基本信息区域 -->
      <div class="panel-section config-info-section">
        <div class="road-info">
          <div class="section-header">
            <h3>路段基本信息</h3>
          </div>

          <div class="road-table">
            <el-table
              :data="currentConfig"
              size="small"
              border
              highlight-current-row
              @current-change="handleRoadSelect"
              ref="roadTableRef"
            >
              <el-table-column type="index" width="50" label="序号" />
              <el-table-column prop="roadName" label="道路名" min-width="120">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.roadName"
                    size="small"
                    @change="syncRoadNameWithAttribute(scope.$index, scope.row.roadName)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="direction" label="方位" width="100">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.direction.id"
                    @change="(value) => handleDirectionChange(scope.row, value)"
                    value-key="id">
                    <el-option
                      v-for="item in getAvailableDirections(scope.row.direction.id)"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="primary"
                    plain
                    @click="editRoadDetail(scope.$index)"
                  >
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 下部分：详细配置标签页 -->
      <div class="panel-section tabs-section">
        <div class="selected-road-info" v-if="selectedRoad">
          <span class="selected-label">当前编辑路段：</span>
          <span class="selected-value">{{ selectedRoad.roadName }}</span>
        </div>
        <div v-if="isTabsReady">
          <el-tabs type="border-card" class="config-tabs" v-model="activeTab">
            <el-tab-pane label="常用属性" name="road">
              <div v-if="!selectedRoad" class="no-road-selected">
                <el-empty description="请先选择一条道路">
                  <el-button type="primary" @click="handleRoadSelect(currentConfig[0])">选择第一条道路</el-button>
                </el-empty>
              </div>
              <RoadConfig
                v-else
                :initialAttributes="currentRoadAttributes"
                :roadId="currentRoadId"
                @change="handleRoadDetailChange"
              />
            </el-tab-pane>
            <el-tab-pane label="进口车道" name="entry">
              <div v-if="!selectedRoad" class="no-road-selected">
                <el-empty description="请先选择一条道路">
                  <el-button type="primary" @click="handleRoadSelect(currentConfig[0])">选择第一条道路</el-button>
                </el-empty>
              </div>
              <EntryLaneConfig
                v-else
                :selectedRoad="selectedRoad"
                :roadId="currentRoadId"
                @change="handleEntryLaneChange"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-else class="loading-placeholder">
          <el-skeleton :rows="3" animated />
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="panel-section action-buttons">
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
        <el-button type="success" @click="exportConfig">导出配置</el-button>
        <el-button @click="resetConfig">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="IntersectionEditor">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick, getCurrentInstance } from 'vue';
import LaneRender from "@/components/IntersectionEditor/src/LaneRender.js";
import RoadConfig from "@/components/IntersectionEditor/src/config/RoadConfig.vue";
import TypeSelector from "@/components/IntersectionEditor/src/config/TypeSelector.vue";
import EntryLaneConfig from "@/components/IntersectionEditor/src/config/EntryLaneConfig.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { debounce } from "lodash-es";
import { getDefaultConfig,defaultCrossConfig,defaultYConfig,defaultTConfig } from '@/components/IntersectionEditor/src/data';

// 导入类型定义
import {
  Road,
  Direction,
  RoadAttributes,
  ConfigData,
  DIRECTIONS,
  INTERSECTION_TYPE_NAMES,
  INTERSECTION_TYPE_ROAD_LIMITS
} from './types';


// 获取当前实例
const { proxy } = getCurrentInstance();

// 画布相关引用和状态
const canvasRef = ref<HTMLCanvasElement | null>(null);
let laneRender: any = null;
const canvasWidth = ref<number>(1000);
const canvasHeight = ref<number>(1000);
const isFullscreen = ref<boolean>(false);
const configError = ref<boolean>(false);
const isTabsReady = ref<boolean>(false);

// 使用导入的常量
const directions = DIRECTIONS;

// 根据当前方向获取可选的相邻方向
const getAvailableDirections = (currentDirectionId: number) => {
  // 方向ID映射关系，每个方向只能选择相邻的方向
  const directionMap = {
    1: [1, 2, 8],  // 北方向只能选择西北、北、东北
    3: [2, 3, 4],  // 东方向只能选择东北、东、东南
    5: [4, 5, 6],  // 南方向只能选择东南、南、西南
    7: [6, 7, 8],  // 西方向只能选择西南、西、西北
    2: [1, 2, 3],  // 东北方向只能选择北、东北、东
    4: [3, 4, 5],  // 东南方向只能选择东、东南、南
    6: [5, 6, 7],  // 西南方向只能选择南、西南、西
    8: [7, 8, 1]   // 西北方向只能选择西、西北、北
  };

  // 如果当前方向在映射中存在，返回可选方向，否则返回所有方向
  return currentDirectionId && directionMap[currentDirectionId]
    ? directions.filter(dir => directionMap[currentDirectionId].includes(dir.value))
    : directions;
};

// Props
const props = defineProps({
  id: {
    type: [Number, String],
    default: null
  },
  // 是否隐藏配置面板
  hideConfig: {
    type: Boolean,
    default: false
  },
  // 接收配置数据
  config: {
    type: [Object, Array, String],
    default: null
  },
  intersectionType: {
    type: String,
    default: 'cross'
  }
});

// 当前配置
const currentConfig = ref<Road[]>([]);

// 路口类型
const intersectionType = ref<string>(typeof props.intersectionType === 'string' ? props.intersectionType : 'cross');

// 当前选中的路段
const selectedRoad = ref<Road | null>(null);
const currentRoadId = ref<number | null>(null);

// 路段表格引用
const roadTableRef = ref(null);

const activeTab = ref<string>('road');

// 定时器
let timer: number | null = null;

// Emits
const emit = defineEmits<{
  (e: 'change', data: ConfigData, id: number | string | null,type:string): void;
  (e: 'save', data: ConfigData, id: number | string | null,type:string): void;
  (e: 'error', message: string): void;
}>();

// 计算属性
const canAddRoad = computed(() => {
  const limit = INTERSECTION_TYPE_ROAD_LIMITS[intersectionType.value] || 4;
  return currentConfig.value.length < limit;
});

const currentRoadAttributes = computed<RoadAttributes>(() => {
  if (!currentRoadId.value || !selectedRoad.value) {
    return {
      'roadName': currentConfig.value[0]?.roadName || '',
      'entryLanes': currentConfig.value[0]?.approach?.lanes?.length || 3,
      'dividerType': currentConfig.value[0]?.dividerType || '2',
      'exitLanes': currentConfig.value[0]?.exit?.lanes || 3,
      'advanceTurn': currentConfig.value[0]?.turnAround || '0',
      'hasLeftTurnWaiting': currentConfig.value[0]?.waitTurnLeft || false,
      'straightWaiting': currentConfig.value[0]?.waitStraight || false,
      'hasPedestrianCrossing': currentConfig.value[0]?.zebraCrossingFlag || true
    };
  }

  return {
    'roadName': selectedRoad.value.roadName,
    'entryLanes': selectedRoad.value.approach.lanes.length,
    'dividerType': selectedRoad.value.dividerType,
    'exitLanes': selectedRoad.value.exit.lanes,
    'advanceTurn': selectedRoad.value.turnAround,
    'hasLeftTurnWaiting': selectedRoad.value.waitTurnLeft,
    'straightWaiting': selectedRoad.value.waitStraight,
    'hasPedestrianCrossing': selectedRoad.value.zebraCrossingFlag
  };
});

// 生命周期钩子
onMounted(async () => {
  // 初始化配置
  initConfig();

  window.addEventListener('resize', handleWindowResize);

  // 初始化 tabs
  await nextTick();
  isTabsReady.value = true;
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
  if (timer !== null) {
    clearInterval(timer);
  }

  // 清理 LaneRender 实例
  if (laneRender) {
    laneRender = null;
  }
});

// 初始化配置
function initConfig(): void {
  configError.value = false;

  // console.log("props.config=",JSON.stringify(props.config));
  // console.log("intersectionType=",props.intersectionType);

  if (props.config) {
    try {
      let parsedConfig;
      console.log("typeof props.config=",typeof props.config);

      if (typeof props.config === 'string') {
        parsedConfig = JSON.parse(props.config);
        console.log("=======");
      } else {
        parsedConfig = props.config;
        console.log("--------");
      }

      // 处理不同格式的配置
      if (Array.isArray(parsedConfig)) {
        // 验证数组中的每个元素是否是有效的道路配置
        if (validateRoadConfig(parsedConfig)) {
          currentConfig.value = parsedConfig;
          intersectionType.value = props.intersectionType;
        } else {
          throw new Error('Invalid road configuration');
        }
        console.log("++++++++++++++");
      } else if (parsedConfig && typeof parsedConfig === 'object') {
        // 如果是对象，检查是否有 type 和 config 属性
        if (parsedConfig.type && Array.isArray(parsedConfig.config)) {
          if (validateRoadConfig(parsedConfig.config)) {
            console.log("parsedConfig.config=",parsedConfig.config)
            intersectionType.value = props.intersectionType;
            currentConfig.value = parsedConfig.config;
          } else {
            throw new Error('Invalid road configuration in config property');
          }
        } else {
          throw new Error('Missing type or config property');
        }
      } else {
        throw new Error('Unrecognized config format');
      }
    } catch (error:any) {
      console.error('配置格式错误:', error);
      configError.value = true;
      emit('error', `配置格式错误: ${error.message}`);
      currentConfig.value = getDefaultConfig('cross');
    }
  } else {
    console.error('未提供配置，使用默认配置');
    currentConfig.value = getDefaultConfig('cross');
  }

  const configSnapshot = JSON.parse(JSON.stringify(currentConfig.value));
  // console.log('currentConfig=', currentConfig.value);


  nextTick(() => {
    initLaneRender(configSnapshot);
  });
}

// 验证道路配置
function validateRoadConfig(roads: any[]): boolean {
  if (!Array.isArray(roads) || roads.length === 0) {
    return false;
  }

  for (const road of roads) {
    if (!road || typeof road !== 'object') return false;
    if (!road.roadId || !road.direction || !road.direction.id) return false;
    if (!road.approach || !Array.isArray(road.approach.lanes) || road.approach.lanes.length === 0) return false;
    if (!road.exit || typeof road.exit.lanes !== 'number') return false;
  }

  return true;
}

// 初始化车道渲染器
function initLaneRender(configData?: any[]): void {
  if (!canvasRef.value) return;

  try {
    // 确保之前的实例被清理
    if (laneRender) {
      laneRender = null;
    }

    // 确保 canvas 尺寸正确设置
    canvasWidth.value = canvasRef.value.parentElement?.offsetWidth || 1000;
    canvasHeight.value = canvasRef.value.parentElement?.offsetHeight || 1000;

    // 使用传入的配置数据或当前配置
    const dataToUse = configData || JSON.parse(JSON.stringify(currentConfig.value));
    // console.log('initLaneRender中的currentConfig=', dataToUse);

    laneRender = new LaneRender({
      canvas: canvasRef.value,
      data: dataToUse,
      width: canvasWidth.value,
      height: canvasHeight.value
    });
  } catch (error:any) {
    console.error('Error initializing LaneRender:', error);
    emit('error', `初始化渲染器失败: ${error.message}`);
  }
}

// 窗口尺寸变化处理（使用防抖优化性能）
const handleWindowResize = debounce(() => {
  if (laneRender && canvasRef.value) {
    canvasWidth.value = canvasRef.value.parentElement?.offsetWidth || 1000;
    canvasHeight.value = canvasRef.value.parentElement?.offsetHeight || 1000;
    window.dispatchEvent(new Event('resize'));
  }
}, 200);

// 路口类型相关方法
const getTypeDisplayName = (type: string): string => {
  return INTERSECTION_TYPE_NAMES[type] || '未知类型';
};

const getTypeRoadLimit = (type: string): number => {
  return INTERSECTION_TYPE_ROAD_LIMITS[type] || 4;
};

const handleTypeChange = (type: string): void => {
  if (type !== intersectionType.value) {
    ElMessageBox.confirm(
      `确定要将路口类型从"${getTypeDisplayName(intersectionType.value)}"更改为"${getTypeDisplayName(type)}"吗？这将重置所有道路配置。`,
      '更改路口类型',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      intersectionType.value = type;
      updateConfigByType(type);
    }).catch(() => {
      // 用户取消操作，恢复原来的值
    });
  }
};

// 根据类型更新配置
const updateConfigByType = (type: string): void => {
  // 使用数据文件中的默认配置
  currentConfig.value = getDefaultConfig(type);

  // 更新渲染器
  if (laneRender) {
    try {
      laneRender.setData([...currentConfig.value]);
    } catch (error:any) {
      console.error('Error updating LaneRender data:', error);
      emit('error', `更新渲染器数据失败: ${error.message}`);
    }
  }

  // 重置选中状态
  selectedRoad.value = null;
  currentRoadId.value = null;

  // 触发变更事件
  emitChange();
};

// 路段选择处理
const handleRoadSelect = (road: Road): void => {
  selectedRoad.value = road;
  currentRoadId.value = road?.roadId || null;
};

// 同步道路名称更新
const syncRoadNameWithAttribute = (index, newName) => {
  if (index >= 0 && index < currentConfig.value.length) {
    currentConfig.value[index].roadName = newName;

    // 如果当前选中的是该道路，更新选中状态
    if (selectedRoad.value && selectedRoad.value.roadId === currentConfig.value[index].roadId) {
      selectedRoad.value = currentConfig.value[index];
    }

    emitChange();
  }
};


const handleDirectionChange = (road, directionId) => {
  // 从 directions 数组中找到方向名称
  const direction = directions.find(d => d.value === directionId);

  // 更新方向对象中的 ID 和名称
  road.direction = {
    id: directionId,
    name: direction ? direction.label : '未知'
  };

  // 如果道路在当前配置中，更新渲染器
  if (laneRender) {
    laneRender.setData([...currentConfig.value]);
  }

  // 触发变更事件
  emitChange();
};

const editRoadDetail = (index) => {
  if (index >= 0 && index < currentConfig.value.length) {
    selectedRoad.value = currentConfig.value[index];
    currentRoadId.value = currentConfig.value[index].roadId;

    // 选中对应的行
    if (roadTableRef.value) {
      roadTableRef.value.setCurrentRow(currentConfig.value[index]);
      activeTab.value = 'road';
    }
  }
};



// 处理道路详细信息变更
const handleRoadDetailChange = ({ roadId, attributes }: { roadId: number, attributes: RoadAttributes }): void => {
  if (!roadId || !attributes) return;

  const roadIndex = currentConfig.value.findIndex(road => road.roadId === roadId);

  if (roadIndex >= 0) {
    // 更新道路属性
    currentConfig.value[roadIndex].roadName = attributes.roadName;
    currentConfig.value[roadIndex].dividerType = attributes.dividerType;
    currentConfig.value[roadIndex].exit.lanes = attributes.exitLanes;
    currentConfig.value[roadIndex].turnAround = attributes.advanceTurn;
    currentConfig.value[roadIndex].waitTurnLeft = attributes.hasLeftTurnWaiting;
    currentConfig.value[roadIndex].waitStraight = attributes.straightWaiting;
    currentConfig.value[roadIndex].zebraCrossingFlag = attributes.hasPedestrianCrossing;

    // 处理进口车道数变化
    const currentLanesCount = currentConfig.value[roadIndex].approach.lanes.length;
    const newLanesCount = attributes.entryLanes;

    if (newLanesCount > currentLanesCount) {
      // 增加车道
      const inc = newLanesCount - currentLanesCount;
      const templateLane = currentConfig.value[roadIndex].approach.lanes[currentLanesCount-1] || {
        "nThrough": 1,
        "nTurnLeft": 0,
        "nTurnRight": 0,
        "nTurnAround": 0
      };

      for (let i = 0; i < inc; i++) {
        currentConfig.value[roadIndex].approach.lanes.push(JSON.parse(JSON.stringify(templateLane)));
      }
    } else if (newLanesCount < currentLanesCount) {
      // 减少车道
      currentConfig.value[roadIndex].approach.lanes.splice(newLanesCount, currentLanesCount - newLanesCount);
    }

    // 更新渲染器数据
    if (laneRender) {
      try {
        laneRender.setData([...currentConfig.value]);
      } catch (error) {
        console.error('Error updating LaneRender data:', error);
      }
    }

    // 如果当前选中的是该道路，更新选中状态
    if (selectedRoad.value && selectedRoad.value.roadId === roadId) {
      selectedRoad.value = currentConfig.value[roadIndex];
    }

    emitChange();
  }
};

// 处理进口车道配置变化
const handleEntryLaneChange = ({ roadId, lanes }: { roadId: number, lanes: any[] }): void => {
  if (roadId && lanes) {
    // 找到对应道路的索引
    const roadIndex = currentConfig.value.findIndex(road => road.roadId === roadId);

    if (roadIndex !== -1) {
      // 更新道路的进口车道配置
      currentConfig.value[roadIndex].approach.lanes = lanes;

      // 更新渲染器数据
      if (laneRender) {
        try {
          laneRender.setData([...currentConfig.value]);
        } catch (error) {
          console.error('Error updating LaneRender data:', error);
        }
      }

      // 如果当前选中的是该道路，更新选中状态
      if (selectedRoad.value && selectedRoad.value.roadId === roadId) {
        selectedRoad.value = currentConfig.value[roadIndex];
      }

      // 触发变更事件
      emitChange();

      ElMessage.success('进口车道配置已更新');
    }
  }
};

// 导出图片
const exportImage = (type: string = 'image/png', quality: number = 0.92): void => {
  try {
    if (!canvasRef.value) return;

    const dataUrl = canvasRef.value.toDataURL(type, quality);
    if (dataUrl === 'data:,') {
      ElMessage.error('Canvas 内容为空或有安全限制');
      return;
    }

    const link = document.createElement('a');
    const extension = type === 'image/png' ? 'png' : 'jpg';
    link.download = `intersection-${new Date().toISOString().split('T')[0]}.${extension}`;
    link.href = dataUrl;

    document.body.appendChild(link);
    link.click();

    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    }, 100);

    ElMessage.success(`已保存为${extension.toUpperCase()}图片`);
  } catch (error: any) {
    console.error('保存画布为图片时发生错误:', error);
    ElMessage.error(`保存图片失败: ${error.message}`);
  }
};

// 切换全屏
const toggleFullscreen = (): void => {
  isFullscreen.value = !isFullscreen.value;
  nextTick(() => {
    if (canvasRef.value) {
      window.dispatchEvent(new Event('resize'));
    }
  });
};

// 保存配置
const saveConfig = (): void => {
  try {
    const configData: ConfigData = currentConfig.value;
    emit('save', configData, props.id,intersectionType.value);
    ElMessage.success('配置已保存');
  } catch (error) {
    console.error('保存配置时发生错误:', error);
    ElMessage.error('保存配置失败');
  }
};

// 导出配置
const exportConfig = (): void => {
  try {
    const configData = currentConfig.value;
    const jsonString = JSON.stringify(configData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `intersection-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    ElMessage.success('配置已导出');
  } catch (error) {
    console.error('导出配置时发生错误:', error);
    ElMessage.error('导出配置失败');
  }
};

// 重置配置
const resetConfig = (): void => {
  ElMessageBox.confirm(
    '确定要重置所有配置吗？这将丢失所有未保存的更改。',
    '重置配置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    intersectionType.value = 'cross';
    updateConfigByType('cross');
    selectedRoad.value = null;
    currentRoadId.value = null;
    configError.value = false;
    ElMessage.success('配置已重置');
  }).catch(() => {
    // 用户取消操作
  });
};

// 发送变更事件
const emitChange = (): void => {
  emit('change', currentConfig.value,props.id,intersectionType.value);
};

// 监听类型变化
watch(intersectionType, (newType) => {
  updateConfigByType(newType);
});

// 添加对 props.config 和 props.intersectionType 的监听
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    initConfig();
  }
}, { deep: true });

watch(() => props.intersectionType, (newType) => {
  if (newType && newType !== intersectionType.value) {
    intersectionType.value = newType;
  }
});
</script>

<style scoped>
.lane {
  width: 100%;
  height: 100%;
  background-color: #325e76;
}

.intersection-editor {
  display: flex;
  width: 100%;
  height: calc(100vh - 100px);
  overflow: hidden;
  position: relative;
}

.intersection-editor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: white;
}

/* 错误提示 */
.config-error-message {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  z-index: 100;
}

/* 左侧画布区域 */
.canvas-area {
  flex: 2;
  position: relative;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 0; /* 防止flex项溢出 */
  height: 100%; /* 确保高度填满父容器 */
}

.canvas-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 40px 0;
}

.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.canvas-tools {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 右侧配置面板 */
.config-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid #dcdfe6;
  background-color: #fff;
  overflow: hidden;
  width: 33%;
  max-width: 540px;
  min-width: 480px;
  height: 100%; /* 确保高度填满父容器 */
}

/* 面板各区域布局 */
.panel-section {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

/* 上部分：类型选择器 */
.type-selector-section {
  flex-shrink: 0;
}

/* 中部分：路段信息 */
.config-info-section {
  flex: 1;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.section-tools {
  display: flex;
  gap: 8px;
}

/* 下部分：标签页 */
.tabs-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.selected-road-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px 0;
}

.selected-label {
  font-weight: bold;
  margin-right: 5px;
}

.selected-value {
  color: var(--el-color-primary);
}

/* 路段列表样式 */
.road-list {
  margin-bottom: 10px;
}

.road-list-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.road-list-item:hover {
  background-color: #f5f7fa;
}

.road-list-item.active {
  background-color: #ecf5ff;
  color: var(--el-color-primary);
}

.road-list-item-name {
  flex: 1;
  font-weight: 500;
}

.road-list-item-direction {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

/* 标签页内容区域 */
.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

/* 无数据状态 */
.no-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.no-data-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-data-text {
  font-size: 14px;
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  border-top: 1px solid #ebeef5;
}

.action-button {
  margin-right: 8px;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .intersection-editor {
    flex-direction: column;
    height: auto;
  }

  .canvas-area {
    height: 60vh;
    min-height: 400px;
  }

  .config-panel {
    width: 100%;
    max-width: none;
    min-width: 0;
    border-left: none
  }
}

.loading-placeholder {
  padding: 20px;
  min-height: 200px;
}
</style>
