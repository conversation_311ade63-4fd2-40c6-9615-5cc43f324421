// 通道组件相关类型定义

export interface ChannelItem {
  id: number;
  desc?: string;
  turn?: number[];
  type?: number;
  controlsource?: number;
  direction?: number;
  controltype?: number;
  typeAndSouce?: number[];
  voltthresh?: number;
  pacthresh?: number;
  peakhthresh?: number;
  peaklthresh?: number;
  realdir?: number[];
  realtype?: string;
}

export interface TypeOption {
  value: number;
  label: string;
  children?: TypeOption[];
}

export interface DirectionItem {
  id: number | string;
  color: string;
  peddirection?: PedestrianDirection[];
}

export interface PedestrianDirection {
  name: string;
  id: number;
  color: string;
}

export interface PhaseItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
  controltype?: number;
}

export interface OverlapItem {
  id: number;
  direction?: number[];
  peddirection?: number[];
}

export interface SidewalkPhaseData {
  phaseid: number;
  id: number;
  name: string;
}

export interface ChannelTypeOption {
  value: number;
  label: string;
}

export interface TurnOption {
  value: number;
  label: string;
}

export interface SignLocationOption {
  value: number;
  label: string;
}

export interface TurnListItem {
  label: string;
  children: TurnOption[];
}

export interface RealDirOption {
  value: number;
  item: DirectionItem[];
  label: string;
}

// 全局参数模型接口
export interface GlobalParamModel {
  getParamsByType: (type: string) => any[];
  getParamLength: (type: string) => number;
  addParamsByType: (type: string, data: any) => void;
  deleteParamsByType: (type: string, index: number, count: number) => void;
}

// 组件数据接口
export interface ChannelComponentData {
  tableHeight: number;
  listLoading: boolean;
  id: number;
  typeOptions: TypeOption[];
  sidewalkPhaseData: SidewalkPhaseData[];
  Data: {
    left: string;
    top: string;
  };
  Datas: {
    left: string;
    top: string;
  };
  channeltypeOptions: ChannelTypeOption[];
  turnOptions: Map<number, TurnOption[]>;
  realdirOptions: Map<number, RealDirOption[]>;
  carList: TurnOption[];
  peopleList: TurnOption[];
  turnList: TurnListItem[];
  signLocationList: SignLocationOption[];
  desclist: Map<number, string[]>;
  diagramVisible: boolean;
}

// 点击外部指令接口
export interface ClickOutsideBinding {
  expression?: string;
  value?: (e: Event) => void;
}

export interface ClickOutsideElement extends HTMLElement {
  __vueClickOutside__?: (e: Event) => void;
}
