<template>
    <div class="plan-table" ref="planTableRef">
        <div style="display: inline-block;" ref="ignoreClickOutsideRef">
            <el-button style="margin-bottom: 10px" type="primary" @click="onAdd">
                添加
            </el-button>
            <el-button style="margin-bottom: 10px" type="primary" @click="handleSort">
                一键排序
            </el-button>
            <!-- <span class="coordination">
                协调计划:
                <el-switch v-model="coorDinations" :active-value="1" :inactive-value="0" @change="changeDination" />
            </span> -->
        </div>
        <div ref="tableContainerRef">
            <el-table v-if="visible" ref="planTableRef" :data="plan" v-loading="listLoading"
                element-loading-text="Loading" fit highlight-current-row :max-height="tableHeight" id="footerBtn"
                @row-click="handleRowClick" class="plan-data-table">
                <el-table-column label="时间" min-width="190" align="center">
                    <template #default="scope">
                        <div class="time-input-container">
                            <el-row :gutter="4" justify="center" align="middle">
                                <el-col :span="10">
                                    <el-select v-model="scope.row.hour" filterable style="width: 150px" size="small"
                                        @focus="handleRowClick(scope.row)">
                                        <el-option v-for="item in hoursOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-col>
                                <el-col :span="2" style="text-align: center; display: flex; align-items: center; justify-content: center;">
                                    <span style="font-weight: bold; font-size: 16px;">:</span>
                                </el-col>
                                <el-col :span="10">
                                    <el-autocomplete v-model="scope.row.minute" style="width: 150px" class="inline-input"
                                        size="small" :maxlength="2" :fetch-suggestions="querySearch" @select="handleSelect"
                                        @change="(val) => onMinuteChange(val, scope.row)" />
                                </el-col>
                            </el-row>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="控制方式" min-width="100" align="center">
                    <template #default="scope">
                        <el-select v-model="scope.row.control" size="small" @change="doChange(scope.row)"
                            @focus="handleRowClick(scope.row)">
                            <el-option v-for="item in controlOptions" :key="item.value"
                                :label="getControlOptionLabel(item.value)" :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="方案" min-width="100" align="center">
                    <template #default="scope">
                        <el-select v-model="scope.row.pattern" size="small" :disabled="handleControl(scope.row)"
                            @focus="handleRowClick(scope.row)">
                            <el-option v-for="item in patternOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="周期" align="center">
                    <template #default="scope">
                        <span v-if="scope.row.pattern">
                            {{ getPatternCycle(scope.row.pattern) }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="danger" link @click="handleDelete(scope.$index)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/modules/globalParam'
import { getGlobalParamsMgr } from '@/components/EdgeMgr/globalManager'
import type { Ref } from 'vue'

// 定义接口类型
interface PlanItem {
    id: number
    hour: number
    minute: number | string
    control: number
    pattern?: number
}

interface OptionItem {
    value: number
    label?: string
    cycle?: number
}

interface MinuteOption {
    value: number
    label: string
}

// Props定义
interface Props {
    plan: PlanItem[]
    planid: number
    planname: string
    coordinate: number
}

// Emits定义
interface Emits {
    (e: 'update:plan', plan: PlanItem[]): void
    (e: 'add-item', item: PlanItem): void
    (e: 'delete-item', index: number): void
    (e: 'sort-plan', sortedPlan: PlanItem[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 组合式API - 使用 Pinia store
const globalStore = useGlobalStore()
const { tscParam, sortPlanList } = storeToRefs(globalStore)

// 响应式引用
const planTableRef = ref()
const tableContainerRef = ref()
const ignoreClickOutsideRef = ref()
const tableHeight = ref(600) // 增加初始高度
const coorDinations = ref(props.coordinate)
const screenHeight = ref(window.innerHeight)
const id = ref(1)
const listLoading = ref(false)
const controlStatus = ref(true)
const visible = ref(true)
const curClickedRow: Ref<PlanItem | null> = ref(null)

// 计算属性 - 从 Pinia store 获取响应式数据
const planList = computed(() => {
    try {
        return tscParam.value?.planList || []
    } catch (error) {
        console.error('Error accessing planList from store:', error)
        return []
    }
})

// 小时选项
const hoursOptions: OptionItem[] = Array.from({ length: 24 }, (_, i) => ({
    value: i,
    label: i.toString().padStart(2, '0')
}))

// 分钟选项（只包含特定的分钟值）
const minuteOptions: MinuteOption[] = [
    { value: 0, label: '00' },
    { value: 5, label: '05' },
    { value: 10, label: '10' },
    { value: 15, label: '15' },
    { value: 20, label: '20' },
    { value: 25, label: '25' },
    { value: 30, label: '30' },
    { value: 35, label: '35' },
    { value: 40, label: '40' },
    { value: 45, label: '45' },
    { value: 50, label: '50' },
    { value: 55, label: '55' },
    { value: 59, label: '59' }
]

// 方案选项（响应式）
const patternOptions = ref<OptionItem[]>([])

// 控制选项
const controlOptions: OptionItem[] = [
    { value: 5 },
    { value: 10 },
    { value: 6 },
    { value: 1 },
    { value: 2 },
    { value: 3 },
    { value: 9 },
    { value: 12 },
    { value: 18 },
    { value: 19 }
]

// 全局参数模型
let globalParamModel: any = null

// 控制选项标签映射
const getControlOptionLabel = (value: number): string => {
    const controlLabels: { [key: number]: string } = {
        0: '恢复自主',
        1: '黄闪',
        2: '全红',
        3: '关灯',
        5: '定周期控制',
        6: '自适应',
        9: '方案生成',
        10: '无电缆控制',
        12: '行人过街控制',
        18: '感应式自适应',
        19: '感应式行人过街控制'
    }
    return controlLabels[value] || `控制方式${value}`
}

// 初始化方法
const initializePatternOptions = () => {
    try {
        globalParamModel = getGlobalParamsMgr()
        if (!globalParamModel) {
            console.warn('globalParamModel is not available')
            return
        }

        const patternList = globalParamModel.getParamsByType('patternList') || []

        patternOptions.value = patternList.map((pattern: any) => ({
            value: pattern.id,
            label: pattern.desc || `方案${pattern.id}`,
            cycle: pattern.cycle
        }))

        // 清理无效的pattern
        if (props.plan && Array.isArray(props.plan)) {
            props.plan.forEach((planItem: PlanItem) => {
                const patternValues = patternOptions.value.map(p => p.value)
                if (planItem.pattern && !patternValues.includes(planItem.pattern)) {
                    delete planItem.pattern
                }
            })
        }

        // 添加协调控制选项
        if (planList.value && Array.isArray(planList.value)) {
            planList.value.forEach((item: any) => {
                if (item.coordinate === 1 && item.id === props.planid) {
                    const existingOption = controlOptions.find(opt => opt.value === 0)
                    if (!existingOption) {
                        controlOptions.push({ value: 0 })
                    }
                }
            })
        }

        increaseId()
    } catch (error) {
        console.error('Error initializing pattern options:', error)
    }
}

// 设置表格最大高度
const setTableMaxHeight = () => {
    nextTick(() => {
        calculateTableHeight()

        // 监听窗口大小变化
        window.addEventListener('resize', calculateTableHeight)
    })
}

// 计算表格高度
const calculateTableHeight = () => {
    try {
        const container = tableContainerRef.value
        if (container) {
            // 获取容器到视口顶部的距离
            const containerTop = container.getBoundingClientRect().top
            // 计算可用高度：视口高度 - 容器顶部位置 - 底部预留空间
            const availableHeight = window.innerHeight - containerTop - 100
            // 设置最小高度和最大高度
            const minHeight = 300
            const maxHeight = Math.max(availableHeight, minHeight)

            tableHeight.value = maxHeight
        }
    } catch (error) {
        console.warn('Error calculating table height:', error)
        // 如果计算失败，使用默认高度
        tableHeight.value = 600
    }
}

// 点击外部处理
const handleClickOutside = (event: Event) => {
    if (tableContainerRef.value && !tableContainerRef.value.contains(event.target as Node)) {
        if (ignoreClickOutsideRef.value && ignoreClickOutsideRef.value.contains(event.target as Node)) return
        handleRowClick()
    }
}

// 监听器
watch(screenHeight, () => {
    calculateTableHeight()
})

// 监听数据变化，重新计算高度
watch(() => props.plan.length, () => {
    nextTick(() => {
        calculateTableHeight()
    })
})

watch(sortPlanList, (val) => {
    if (val) {
        sortAllPlan()
    }
})

// 方法定义
const querySearch = (query: string, cb: (results: MinuteOption[]) => void) => {
    const queryString = query.toString()
    const results = queryString
        ? minuteOptions.filter(createFilter(queryString))
        : minuteOptions
    cb(results)
}

const createFilter = (queryString: string) => {
    return (option: MinuteOption) => {
        return option.label.toLowerCase().indexOf(queryString.toLowerCase()) === 0
    }
}

const handleSelect = (item: MinuteOption) => {
    console.log(item)
}

const onMinuteChange = (val: string, row: PlanItem) => {
    let res = Number(val) || 0
    if (res < 0 || res > 59) {
        res = 0
    }
    row.minute = res
}

const changeDination = (val: number) => {
    if (val === 1) {
        controlOptions.push({ value: 0 })
    } else {
        const index = controlOptions.findIndex(i => i.value === 0)
        if (index > -1) {
            controlOptions.splice(index, 1)
        }
    }
    planList.value.forEach((item: any) => {
        if (item.id === props.planid) {
            item.coordinate = val
        }
    })
}

const increaseId = () => {
    // 实现id在之前的基础上寻找最小的
    const planIdList = props.plan.map(ele => ele.id)

    // 从1开始寻找最小的未使用的id
    for (let j = 1; j <= 48; j++) {
        if (!planIdList.includes(j)) {
            id.value = j
            return
        }
    }

    // 如果所有id都被使用了，使用下一个可用的id
    id.value = props.plan.length + 1
}

const handleDelete = (index: number) => {
    emit('delete-item', index)
    ElMessage({
        type: 'success',
        message: '删除成功！'
    })
}

const onAdd = () => {
    increaseId()
    if (props.plan.length >= 48) {
        ElMessage.error('最多只能创建48条数据!')
        return
    }

    const newPlanItem: PlanItem = {
        id: id.value,
        hour: 0,
        minute: 0,
        control: 5
    }

    emit('add-item', newPlanItem)

    // 添加数据后，等待DOM更新，然后滚动到新添加的行
    nextTick(() => {
        if (planTableRef.value) {
            // 滚动到表格底部，显示新添加的行
            const tableBody = planTableRef.value.$el.querySelector('.el-table__body-wrapper')
            if (tableBody) {
                tableBody.scrollTop = tableBody.scrollHeight
            }
        }
        // 重新计算表格高度
        calculateTableHeight()
    })
}

const compareProperty = (property: keyof PlanItem) => {
    return (a: PlanItem, b: PlanItem) => {
        const value1 = a[property] as number
        const value2 = b[property] as number
        return value1 - value2
    }
}

const doChange = (row: PlanItem) => {
    if (row.control === 1 || row.control === 2 || row.control === 3) {
        if (row.pattern !== undefined) {
            delete row.pattern
        }
    }
}

const handleControl = (row: PlanItem) => {
    return (
        row.control === undefined ||
        row.control === 0 ||
        row.control === 1 ||
        row.control === 2 ||
        row.control === 3
    )
}

const handleSort = () => {
    if (!props.plan || props.plan.length === 0) {
        ElMessage.warning('没有数据可以排序')
        return
    }

    // 创建当前数据的副本进行排序
    const sortedPlan = [...props.plan].sort((a, b) => {
        // 首先按小时排序
        if (a.hour !== b.hour) {
            return a.hour - b.hour
        }
        // 如果小时相同，按分钟排序
        const aMinute = typeof a.minute === 'string' ? parseInt(a.minute) || 0 : a.minute
        const bMinute = typeof b.minute === 'string' ? parseInt(b.minute) || 0 : b.minute
        return aMinute - bMinute
    })

    // 通过事件通知父组件更新排序后的数据
    emit('sort-plan', sortedPlan)

    // 解决select聚焦效果未取消
    visible.value = false
    nextTick(() => {
        visible.value = true
        ElMessage.success('排序完成')
    })
}

const handleRowClick = (row?: PlanItem) => {
    if (row && planTableRef.value) {
        planTableRef.value.setCurrentRow(row)
        curClickedRow.value = row
    } else {
        curClickedRow.value = null
    }
}

const sortAllPlan = () => {
    // 下载前排序刷新表格
    visible.value = false
    nextTick(() => {
        visible.value = true
        globalStore.SetPlanListOrder(false)
    })
}

// 获取方案周期
const getPatternCycle = (patternId: number) => {
    const pattern = patternOptions.value.find(item => item.value === patternId)
    return pattern?.cycle || ''
}

// 生命周期钩子
onMounted(() => {
    initializePatternOptions()
    setTableMaxHeight()
    document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
    window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.plan-table {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.coordination {
    margin-left: 20px;
    font-weight: bold;
}

.inline-input {
    width: 80px;
}

/* 表格容器样式 */
.plan-data-table {
    flex: 1;
    overflow: auto;
}

/* 确保表格能够正确显示滚动条 */
:deep(.el-table__body-wrapper) {
    overflow-y: auto !important;
}

/* 优化表格行的显示 */
:deep(.el-table__row) {
    transition: background-color 0.2s ease;
}

:deep(.el-table__row:hover) {
    background-color: #f5f7fa;
}

/* 表格内容居中显示 */
:deep(.el-table td) {
    text-align: center !important;
}

:deep(.el-table th) {
    text-align: center !important;
}

/* 表格内的表单控件居中 */
:deep(.el-table .el-select) {
    margin: 0 auto;
}

:deep(.el-table .el-autocomplete) {
    margin: 0 auto;
}

:deep(.el-table .el-button) {
    margin: 0 auto;
}

/* 确保行内容垂直居中 */
:deep(.el-table .cell) {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
}

/* 时间输入容器样式 */
.time-input-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* 时间输入行样式 */
.time-input-container .el-row {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* 时间分隔符样式 */
.time-input-container .el-col:nth-child(2) {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
}
</style>
