<template>
    <div class="mb-2">
        <el-radio-group v-model="radio">
            <el-radio value="1" size="large">环配置</el-radio>
            <el-radio value="2" size="large">阶段配置</el-radio>
        </el-radio-group>
    </div>
    <el-table :data="tableData" :border="parentBorder" :preserve-expanded-content="preserveExpanded"
        style="width: 100%">
        <el-table-column type="expand">
            <template #default="props">
                <div v-if="isTabsReady">
                    <el-tabs tab-position="top" class="config-tabs" v-if="radio == '1'" style="padding: 5px;">
                        <el-tab-pane label="环配置">
                            <RingConfig />
                        </el-tab-pane>
                        <el-tab-pane label="扩展参数配置">
                            <ExtendConfig />
                        </el-tab-pane>
                        <el-tab-pane label="其他配置">
                            <el-card>
                                其他配置
                            </el-card>
                        </el-tab-pane>
                    </el-tabs>
                    <el-tabs tab-position="top" class="config-tabs" v-if="radio == '2'">
                        <el-tab-pane label="阶段配置">
                            <StageConfig />
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div v-else class="loading-placeholder">
                    <el-skeleton :rows="2" animated />
                </div>
            </template>
        </el-table-column>
        <el-table-column label="方案编号" prop="patternNo" />
        <el-table-column label="方案名称" prop="patternName">
            <template #default="scope">
                <div @dblclick="startEdit(scope.row, 'patternName')" v-if="!scope.row.isEditing">
                    {{ scope.row.patternName }}
                </div>
                <el-input v-else v-model="scope.row.patternName" @blur="saveEdit" @keyup.enter="saveEdit" />
            </template>
        </el-table-column>
        <el-table-column label="相位差" prop="phaseDiff" />
        <el-table-column label="相位图" prop="patternName" width="600">
            <PatternPreview></PatternPreview>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
            <template #default="scope">
                <el-button v-if="scope.$index === tableData.length - 1" link type="primary" size="small"
                    @click.prevent="copyRow(scope.$index)">
                    复制
                </el-button>
                <el-button link type="danger" size="small" @click.prevent="copyRow(scope.$index)">
                    删除
                </el-button>
            </template>
        </el-table-column>
    </el-table>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'

import PatternPreview from '@/components/PatternPreview/index.vue';
import ExtendConfig from './extendConfig/index.vue';
import RingConfig from './ringConfig/index.vue';
import StageConfig from './stageConfig/index.vue';

const editingCell = ref(null)
const radio = ref('1')
const parentBorder = ref(false)
const preserveExpanded = ref(false)
const patternNo = ref('1')
const isTabsReady = ref(false)

// 初始化组件
onMounted(async () => {
    await nextTick()
    isTabsReady.value = true
})
const tableData = ref([
    {
        patternNo: patternNo.value,
        patternName: '方案' + patternNo.value,
        phaseDiff: '0',
    },
])

const copyRow = (index: number) => {
    const original = tableData.value[index]
    const copy = {
        ...original,
        patternNo: (parseFloat(original.patternNo) + 1).toString(),
        patternName: `${original.patternName.slice(0, -1)}${parseFloat(original.patternNo) + 1}`, // 剔除最后一个字符并拼接新编号
        isNew: true
    }
    tableData.value.splice(index + 1, 0, copy)
}


const startEdit = (row, field) => {
    // 防止重复点击同一个单元格
    if (row.isEditing && row.editingField === field) return

    // 保存当前编辑的字段
    row.editingField = field
    row.isEditing = true

    // 记录当前编辑的单元格引用
    editingCell.value = row
}

const saveEdit = () => {
    if (!editingCell.value) return

    // 结束编辑状态
    editingCell.value.isEditing = false
    editingCell.value.editingField = null
    editingCell.value = null
}
</script>

<style scoped>
.config-tabs>>>.el-tabs__content {
    padding: 0px;
}

/* 添加编辑状态样式 */
.el-input {
    width: 95%;
    margin: auto;
}

/* 双击编辑提示 */
td[role='cell']>div:hover {
    background-color: #f5f7fa;
    cursor: pointer;
}

.loading-placeholder {
    padding: 20px;
    min-height: 200px;
}
</style>