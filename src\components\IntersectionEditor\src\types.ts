/**
 * 方向定义
 */
export interface Direction {
  id: number;
  name: string;
}

/**
 * 车道配置
 */
export interface Lane {
  nThrough: number;
  nTurnLeft: number;
  nTurnRight: number;
  nTurnAround: number;
}

/**
 * 进口道配置
 */
export interface Approach {
  lanes: Lane[];
}

/**
 * 出口道配置
 */
export interface Exit {
  lanes: number;
}

/**
 * 道路配置
 */
export interface Road {
  roadId: number;
  roadName: string;
  direction: Direction;
  dividerType: string;
  turnAround: string;
  waitTurnLeft: boolean;
  waitStraight: boolean;
  approach: Approach;
  exit: Exit;
  zebraCrossingFlag: boolean;
}

/**
 * 道路属性
 */
export interface RoadAttributes {
  roadName: string;
  entryLanes: number;
  dividerType: string;
  exitLanes: number;
  advanceTurn: string;
  hasLeftTurnWaiting: boolean;
  straightWaiting: boolean;
  hasPedestrianCrossing: boolean;
}

/**
 * 配置数据
 */
export type ConfigData = Road[];

/**
 * 方向选项
 */
export const DIRECTIONS = [
  { label: '北', value: 1 },
  { label: '东北', value: 2 },
  { label: '东', value: 3 },
  { label: '东南', value: 4 },
  { label: '南', value: 5 },
  { label: '西南', value: 6 },
  { label: '西', value: 7 },
  { label: '西北', value: 8 }
];

/**
 * 路口类型名称映射
 */
export const INTERSECTION_TYPE_NAMES: Record<string, string> = {
  'cross': '十字路口',
  'T': 'T型路口',
  'Y': 'Y型路口'
};

/**
 * 路口类型道路数量限制
 */
export const INTERSECTION_TYPE_ROAD_LIMITS: Record<string, number> = {
  'cross': 4,
  'T': 3,
  'Y': 3
};
