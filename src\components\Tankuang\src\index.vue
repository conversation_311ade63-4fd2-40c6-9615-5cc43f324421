<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElPopover, ElInput, ElButton } from 'element-plus';
import XRDDirSelector from '@/components/XRDDirSelector';
import { getTheme } from '@/utils/theme';
import type {
  DirectionSelectorProps,
  DirectionSelectorEmits
} from './types';
const props = withDefaults(defineProps<DirectionSelectorProps>(), {
  list: () => [],
  index: 0,
  disabled: false,
  showBottomName: false,
  lines: 4,
  rows: 3,
  showSpan: true,
  refresh: false,
  showDirIcon: false,
  laneIconStyle: () => ({ left: '60px', top: '10px' }),
  pedIconStyle: () => ({ left: '60px', top: '10px' })
});
const emit = defineEmits<DirectionSelectorEmits>();
const isVisible = ref(false);
const dialogVisible = ref(true);
const name = ref('');
const isRoutine = ref(true);
const status = ref<number[]>([]);

// 刷新数据
const refreshData = () => {
  // 确保props.imgs存在且为数组
  if (!props.imgs || !Array.isArray(props.imgs)) {
    status.value = [];
    name.value = '';
    return;
  }

  status.value = props.imgs.map(item =>
    props.list.includes(item.id) ? 1 : 0
  );
  name.value = getName();
};

// 获取显示名称
const getName = (): string => {
  return props.imgs
    .filter((_, i) => status.value[i])
    .map(item => item.name)
    .join(',');
};

// 切换车道显示类型
const changeLane = () => isRoutine.value = false;
const changeLanes = () => isRoutine.value = true;

// 处理选择框点击
const boxShow = (index: number) => {
  status.value[index] = status.value[index] ? 0 : 1;
  name.value = getName();
  toParent();
};

// 向父组件传递数据
const toParent = () => {
  emit('finsh', {
    status: status.value,
    index: props.index
  });
};

// 隐藏弹框时的处理
const hidePopver = () => {
  dialogVisible.value = false;
  setTimeout(() => {
    dialogVisible.value = true;
  }, 10);
};

// 获取方向列表
const getDirectionList = (direction: number[]) => {
  if (!direction || direction.length === 0) {
    return [];
  }
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
  const result = direction.map(rec => ({
    id: rec,
    color: color
  }));
  return result;
};
const directionListData = computed(() => getDirectionList(props.list));
// 计算弹框大小
const tankuangSize = computed(() => {
  const lines = props.lines;
  const widths = ((100 / lines) - 2) + '%';
  return {
    width: widths
  };
});

// 初始化数据
refreshData();

// 监听刷新标志和列表变化
watch(() => props.refresh, refreshData);
watch(() => props.list, refreshData, { deep: true, immediate: true });
watch(() => props.imgs, refreshData, { deep: true });
</script>

<template>
  <div class="direction-selector">
    <el-popover
      placement="bottom"
      :width="280"
      v-if="!disabled && dialogVisible"
      v-model:visible="isVisible"
      @before-leave="hidePopver"
    >
      <div class="label">
        <div class="label-text">车道</div>
        <el-button
          v-if="isRoutine"
          @click="changeLane()"
          link
          class="toggle-button"
        >
          拓展车道方向
        </el-button>
        <el-button
          v-else
          @click="changeLanes()"
          link
          class="toggle-button"
        >
          常规车道方向
        </el-button>
      </div>
      <div class="main">
        <template v-for="(item, index) in imgs" :key="item.id">
          <div
            v-if="(item.id > 16 && !isRoutine) || (item.id < 17 && isRoutine)"
            :style="tankuangSize"
            :class="status[index] ? 'phaseSelected' : 'phaseNoSelected'"
            @click="boxShow(index)"
          >
            <div v-if="item.class" :class="item.class" style="border:0px"></div>
            <div
              v-else-if="item.img"
              class="image-icon"
              :style="{
                backgroundImage: `url(${item.img})`,
              }"
            />
          </div>
        </template>
      </div>
      <span  v-show="showBottomName" class="show-span">
        {{ name }}
      </span>
      <template #reference>
        <el-input
          :disabled="disabled"
          size="small"
          v-model="name"
          readonly
          class="selector-input"
        />
      </template>
    </el-popover>

    <!-- 统一的图标显示逻辑 -->
    <template v-if="(disabled || showDirIcon) && list.length > 0">
      <span class="dir-icon-container">
        <XRDDirSelector
          :Data="laneIconStyle"
          :Datas="pedIconStyle"
          :Width="disabled ? '40px' : '60px'"
          :Height="disabled ? '40px' : '60px'"
          :showlist="getDirectionList(list)"
        />
      </span>
    </template>

    <span v-show="showSpan" class="show-span">
      {{ name }}
    </span>

  </div>
</template>

<style scoped lang="scss">
.direction-selector {
  display: inline-block;
  vertical-align: middle;

  .label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    margin-bottom: 10px;
    min-width: 260px; /* 确保有足够空间 */

    .label-text {
      font-weight: 500;
      color: #303133;
      flex-shrink: 0; /* 防止文字被压缩 */
    }

    .toggle-button {
      padding: 0;
      font-size: 12px;
      color: #409eff;
      flex-shrink: 0; /* 防止按钮被压缩 */
      white-space: nowrap; /* 防止按钮文字换行 */

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .main {
    width: 100%;
    padding: 0;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    box-sizing: border-box;
    gap: 2px;
  }

  .phaseNoSelected,
  .phaseSelected {
    border-radius: 4px;
    cursor: pointer;
    height: 56px;
    width: calc(25% - 1.5px);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ecf5ff;
    transition: background-color 0.3s ease;
    flex-shrink: 0;
    flex-grow: 0;
    margin: 0;
  }

  .phaseSelected {
    background-color: #409EFF;

    .image-icon {
      filter: brightness(0) invert(1); // 图标反转白底变黑
    }

    .iconfont {
      color: #ffffff !important;
    }
  }

  .image-icon {
    border: 0;
    background-size: 43px;
    width: 100%;
    height: 56px;
    background-repeat: no-repeat;
    background-position: center;
  }

  // 字体图标样式
  .iconfont {
    font-size: 46px !important;
    color: #606266;
  }

  .show-span {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: #606266;
  }

  .hidden-span {
    display: none;
  }

  .selector-input {
    width: 200px;
    vertical-align: middle;
  }

  // 修复在表格编辑模式下的输入框定位问题
  .selector-input :deep(.el-input__inner) {
    vertical-align: middle;
  }

  .dir-icon-container {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
  }
}

/* 修复el-popover内部布局问题 */
:deep(.el-popover) {
  .label {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    height: 40px !important;
    margin-bottom: 10px !important;
    min-width: 260px !important;

    .label-text {
      flex-shrink: 0 !important;
    }

    .toggle-button {
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }

  .main {
    width: 100% !important;
    max-width: 280px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
    align-content: flex-start !important;
    gap: 2px !important;
    overflow: hidden !important;
  }

  .phaseNoSelected,
  .phaseSelected {
    width: calc(25% - 1.5px) !important;
    min-width: 60px !important;
    max-width: 68px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    height: 56px !important;
    box-sizing: border-box !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    background-color: #ecf5ff !important;
    transition: background-color 0.3s ease !important;
  }

  .phaseSelected {
    background-color: #409EFF !important;
  }

  .image-icon {
    border: 0 !important;
    background-size: 43px !important;
    width: 100% !important;
    height: 56px !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
  }

  .phaseSelected .image-icon {
    filter: brightness(0) invert(1) !important;
  }

  // 字体图标样式
  .iconfont {
    font-size: 46px !important;
    color: #606266 !important;
  }

  .phaseSelected .iconfont {
    color: #ffffff !important;
  }
}
</style>

<style lang="scss">
/* 全局样式修复popover布局 - 不使用scoped，确保能够覆盖popover内部样式 */
.el-popover {
  .label {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    height: 40px !important;
    margin-bottom: 10px !important;
    min-width: 260px !important;

    .label-text {
      font-weight: 500 !important;
      color: #303133 !important;
      flex-shrink: 0 !important;
    }

    .toggle-button {
      padding: 0 !important;
      font-size: 12px !important;
      color: #409eff !important;
      flex-shrink: 0 !important;
      white-space: nowrap !important;
    }
  }

  .main {
    width: 100% !important;
    max-width: 280px !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
    align-content: flex-start !important;
    gap: 2px !important;
    overflow: hidden !important;
  }

  .phaseNoSelected,
  .phaseSelected {
    width: calc(25% - 1.5px) !important;
    min-width: 60px !important;
    max-width: 68px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    height: 56px !important;
    box-sizing: border-box !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    background-color: #ecf5ff !important;
    transition: background-color 0.3s ease !important;
  }

  .phaseSelected {
    background-color: #409EFF !important;
  }

  .image-icon {
    border: 0 !important;
    background-size: 43px !important;
    width: 100% !important;
    height: 56px !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
  }

  .phaseSelected .image-icon {
    filter: brightness(0) invert(1) !important;
  }

  // 字体图标样式
  .iconfont {
    font-size: 46px !important;
    color: #606266 !important;
  }

  .phaseSelected .iconfont {
    color: #ffffff !important;
  }
}

/* 修复表格编辑模式下DirectionSelector的样式问题 */
.tb-edit .direction-selector {
  .selector-input {
    vertical-align: middle !important;
  }

  .selector-input .el-input__inner {
    vertical-align: middle !important;
  }
}
</style>
