// 行人相位描述接口
interface PedestrianPhaseDescription {
  id: number;
  name: string;
}

/**
 * @Description: 根据行人相位的描述id获取对应的描述名字
 * @param list 行人相位ID数组
 * @returns 行人相位描述字符串，多个描述用逗号分隔
 */
export function getPedPhaseDesc(list: number[]): string {
  const images: PedestrianPhaseDescription[] = [{
    id: 1,
    name: '东行人'
  },
    {
      id: 2,
      name: '西行人'
    },
    {
      id: 3,
      name: '南行人'
    },
    {
      id: 4,
      name: '北行人'
    },
    {
      id: 5,
      name: '东行人-上'
    },
    {
      id: 6,
      name: '东行人-下'
    },
    {
      id: 7,
      name: '西行人-上'
    },
    {
      id: 8,
      name: '西行人-下'
    },
    {
      id: 9,
      name: '南行人-左'
    },
    {
      id: 10,
      name: '南行人-右'
    },
    {
      id: 11,
      name: '北行人-左'
    },
    {
      id: 12,
      name: '北行人-右'
    },
    {
      id: 13,
      name: 'X行人-/'
    },
    {
      id: 14,
      name: 'X行人-\\'
    },
    {
      id: 15,
      name: '东西路段行人'
    },
    {
      id: 16,
      name: '南北路段行人'
    },
    {
      id: 17,
      name: '东南行人'
    },
    {
      id: 18,
      name: '西南行人'
    },
    {
      id: 19,
      name: '东北行人'
    },
    {
      id: 20,
      name: '西北行人'
    },
    {
      id: 21,
      name: '东南行人-上'
    },
    {
      id: 22,
      name: '东南行人-下'
    },
    {
      id: 23,
      name: '西南行人-上'
    },
    {
      id: 24,
      name: '西南行人-下'
    },
    {
      id: 25,
      name: '东北行人-上'
    },
    {
      id: 26,
      name: '东北行人-下'
    },
    {
      id: 27,
      name: '西北行人-上'
    },
    {
      id: 28,
      name: '西北行人-下'
    }
  ];

  let str: string = '';
  for (const ll of list) {
    for (const image of images) {
      if (image.id === ll) {
        str = str + ',' + image.name;
      }
    }
  }
  if (str !== '') {
    str = str.substring(1);
  }
  return str;
}
