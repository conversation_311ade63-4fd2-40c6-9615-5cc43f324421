// Channel 组件工具函数

import { getPhaseDesc } from '@/utils/phasedesc';
import { getPedPhaseDesc } from '@/utils/pedphasedesc';
import { getTheme } from '@/utils/theme';
import PhaseDataModel from '@/components/PhaseDataModel';
import type {
  ChannelItem,
  DirectionItem,
  PedestrianDirection,
  SidewalkPhaseData,
  RealDirOption
} from './types';

/**
 * 获取控制类型字符串描述
 */
export function getControlTypestr(val: ChannelItem, channelTypeOptions: any[]): string {
  if (val.type === undefined || val.type === '') {
    return '';
  }
  const found = channelTypeOptions.filter(ele => ele.value === val.type)[0];
  return found ? found.label : '';
}

/**
 * 获取转向描述
 */
export function getTurn(val: ChannelItem, turnOptions: Map<number, any[]>): string {
  if (val.turn !== undefined && val.turn.length > 0 && turnOptions.get(val.id)) {
    const desc: string[] = [];
    for (let i = 0; i < val.turn.length; i++) {
      const found = turnOptions.get(val.id)?.filter(ele => ele.value === val.turn![i])[0];
      if (found) {
        desc.push(found.label);
      }
    }
    return desc.join('');
  }
  return '';
}

/**
 * 获取实际方向描述
 */
export function getRealdir(val: ChannelItem, realdirOptions: Map<number, RealDirOption[]>): string {
  if (val.realdir !== undefined && val.realdir.length > 0 && realdirOptions.get(val.id)) {
    const desc: string[] = [];
    for (let i = 0; i < val.realdir.length; i++) {
      const found = realdirOptions.get(val.id)?.filter(ele => ele.value === val.realdir![i])[0];
      if (found) {
        desc.push(found.label);
      }
    }
    return desc.join('');
  }
  return '';
}

/**
 * 获取方向列表
 */
export function getDirectionList(val: ChannelItem): DirectionItem[] {
  if (val.type === 0 || val.type === 1 || val.type === 3) {
    const dirArr: DirectionItem[] = [];
    const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
    if (val.realdir) {
      for (const rec of val.realdir) {
        const recd: DirectionItem = {
          id: rec,
          color: color
        };
        dirArr.push(recd);
      }
    }
    return dirArr;
  } else if (val.type === 2) {
    const dirArr: DirectionItem[] = [];
    const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
    const recd: DirectionItem = {
      id: '',
      peddirection: val.realdir ? getPeds(val.realdir) : [],
      color: color
    };
    dirArr.push(recd);
    return dirArr;
  } else {
    const dirArr: DirectionItem[] = [];
    const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
    const recd: DirectionItem = {
      id: '',
      peddirection: [],
      color: color
    };
    dirArr.push(recd);
    return dirArr;
  }
}

/**
 * 获取行人方向
 */
export function getPeds(data: number[]): PedestrianDirection[] {
  if (data.length === 0) return [];
  const ped: PedestrianDirection[] = [];
  const peddirections: PedestrianDirection[] = [];
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
  const phaseDataModel = new PhaseDataModel();

  for (const walk of data) {
    const objs: PedestrianDirection = {
      name: phaseDataModel.getSidePos(walk)?.name || '',
      id: walk,
      color: color
    };
    peddirections.push(objs);
  }
  ped.push(...Array.from(new Set(peddirections)));
  return ped;
}

/**
 * 获取方向描述
 */
export function getDirection(val: ChannelItem, signLocationList: any[]): string {
  if (val.direction !== undefined) {
    const found = signLocationList.filter(ele => ele.value === val.direction)[0];
    return found ? found.label : '';
  }
  return '';
}

/**
 * 获取类型和来源描述
 */
export function getTypeAndSouce(ele: ChannelItem, typeOptions: any[]): string {
  if (!ele.typeAndSouce || ele.typeAndSouce.length === 0) return '';
  let source = '';
  let dire = '';
  const desc: string[] = [];

  if (ele.typeAndSouce[0] !== undefined) {
    const found = typeOptions.filter(type => type.value === ele.typeAndSouce[0])[0];
    source = found ? found.label : '';
    desc[0] = source;
  }

  if (ele.typeAndSouce[1] !== undefined) {
    typeOptions.forEach(type => {
      if (type.value === ele.typeAndSouce![0] && type.children && type.children.length) {
        const child = type.children.filter(child => child.value === ele.typeAndSouce![1])[0];
        dire = child ? child.label : '';
      }
    });
    desc[1] = dire;
  }

  return desc[0] + (desc[1] ? '/' : '') + (desc[1] ? desc[1] : '');
}

/**
 * 获取行人相位位置
 */
export function getPedPhasePos(phaseList: any[]): SidewalkPhaseData[] {
  const sidewalkPhaseData: SidewalkPhaseData[] = [];
  const phaseDataModel = new PhaseDataModel();

  phaseList.forEach((ele) => {
    if (ele.peddirection) {
      ele.peddirection.forEach((dir: number) => {
        if (phaseDataModel.getSidePos(dir)) {
          sidewalkPhaseData.push({
            phaseid: ele.id,
            id: dir,
            name: phaseDataModel.getSidePos(dir).name
          });
        }
      });
    }
  });
  return sidewalkPhaseData;
}

/**
 * 获取行人方向（基于sidewalkPhaseData）
 */
export function getPed(data: number, sidewalkPhaseData: SidewalkPhaseData[]): PedestrianDirection[] {
  const ped: PedestrianDirection[] = [];
  const peddirections: PedestrianDirection[] = [];
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';

  for (const walk of sidewalkPhaseData) {
    const objs: PedestrianDirection = {
      name: walk.name,
      id: walk.id,
      color: color
    };
    if (data === walk.id) {
      peddirections.push(objs);
    }
  }
  ped.push(...Array.from(new Set(peddirections)));
  return ped;
}

/**
 * 生成相位状态选项（用于车道灯）
 */
export function generatePhaseStatusOptions(): any[] {
  const color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4';
  const status = [];
  for (let i = 1; i <= 16; i++) {
    status.push({
      item: [{
        id: i,
        color: color
      }],
      label: getPhaseDesc([i]),
      value: i
    });
  }
  return status;
}

/**
 * 比较属性函数
 */
export function compareProperty<T>(property: keyof T) {
  return function (a: T, b: T): number {
    const value1 = a[property] as number;
    const value2 = b[property] as number;
    return value1 - value2;
  };
}

/**
 * 清除空值
 */
export function clearEmptyValues(channel: any[], field: string) {
  for (let i = 0; i < channel.length; i++) {
    if (channel[i][field] === '') {
      delete channel[i][field];
    }
  }
}

/**
 * 清除空数组
 */
export function clearEmptyArrays(channel: any[], field: string) {
  for (let i = 0; i < channel.length; i++) {
    if (channel[i][field] && channel[i][field].length === 0) {
      channel[i][field] = [];
    }
  }
}

/**
 * 增加ID（寻找最小可用ID）
 */
export function increaseId(channelList: any[]): number {
  const channelIdList = channelList.map((ele: ChannelItem) => ele.id);

  for (let j = 1; j <= 40; j++) {
    if (!channelIdList.includes(j)) {
      return j;
    }
  }
  return 1;
}
