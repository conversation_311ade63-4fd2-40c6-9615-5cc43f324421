<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="所属路口" prop="deviceIntersectionId">
              <el-select v-model="queryParams.deviceIntersectionId" placeholder="请选择路口">
                <el-option v-for="dict in intersectionList" :key="dict.id" :label="dict.intersectionName"
                  :value="dict.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input v-model="queryParams.deviceCode" placeholder="请输入设备编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="queryParams.deviceType" placeholder="请选择设备类型" clearable>
                <el-option v-for="dict in utc_intersection_device_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备厂家" prop="deviceVender">
              <el-select v-model="queryParams.deviceVender" placeholder="请选择设备厂家" clearable>
                <el-option v-for="dict in utc_vender_name" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['business:intersectionDevice:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              v-hasPermi="['business:intersectionDevice:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['business:intersectionDevice:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['business:intersectionDevice:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <!-- <el-row :span="12">
        <el-col :span="6" v-for="item in intersectionDeviceList" :key="item.id">
          <el-card style="max-width: 480px; margin-bottom: 10px; margin-right: 10px;">
            <template #header>
              <div class="card-header">
                <span>{{ item.intersectionName + '：' + item.deviceName + '(' + item.deviceCode + ')' }}</span>
              </div>
            </template>
            <el-row :span="12">
              <el-col :span="12">
                <p class="text item">{{ '设备类型:' + item.deviceType }}</p>
              </el-col>
              <el-col :span="12">
                <p class="text item">{{ '设备厂家:' + item.deviceVender }}</p>
              </el-col>
            </el-row>
            <el-row :span="12">
              <el-col :span="12">
                <p class="text item">{{ '安装位置:' + item.installPosition }}</p>
              </el-col>
              <el-col :span="12">
                <p class="text item">{{ '识别方向:' + item.identifyDirection }}</p>
              </el-col>
            </el-row>
            <el-row :span="12">
              <el-col :span="12">
                <p class="text item">{{ '设备IP:' + item.deviceIp }}</p>
              </el-col>
              <el-col :span="12">
                <p class="text item">{{ '设备端口:' + item.devicePort }}</p>
              </el-col>
            </el-row>
            <template #footer>
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(item)"
                  v-hasPermi="['business:intersectionDevice:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="danger" icon="Delete" @click="handleDelete(item)"
                  v-hasPermi="['business:intersectionDevice:remove']"></el-button>
              </el-tooltip>
            </template>
          </el-card>
        </el-col>
      </el-row> -->

      <el-table v-loading="loading" border :data="intersectionDeviceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="所属路口" align="center" prop="intersectionName" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="设备编码" align="center" prop="deviceCode" />
        <el-table-column label="设备名称" align="center" prop="deviceName" />
        <el-table-column label="设备类型" align="center" prop="deviceType">
          <template #default="scope">
            <dict-tag :options="utc_intersection_device_type" :value="scope.row.deviceType" />
          </template>
        </el-table-column>
        <el-table-column label="设备厂家" align="center" prop="deviceVender">
          <template #default="scope">
            <dict-tag :options="utc_vender_name" :value="scope.row.deviceVender" />
          </template>
        </el-table-column>
        <el-table-column label="安装位置" align="center" prop="installPosition">
          <template #default="scope">
            <dict-tag :options="utc_install_position" :value="scope.row.installPosition" />
          </template>
        </el-table-column>
        <el-table-column label="识别方向" align="center" prop="identifyDirection">
          <template #default="scope">
            <dict-tag :options="utc_identify_direction" :value="scope.row.identifyDirection" />
          </template>
        </el-table-column>
        <el-table-column label="连接方式" align="center" prop="connectType">
          <template #default="scope">
            <dict-tag :options="utc_connect_type" :value="scope.row.connectType" />
          </template>
        </el-table-column>
        <el-table-column label="设备IP" align="center" prop="deviceIp" />
        <el-table-column label="设备端口" align="center" prop="devicePort" />
        <!-- <el-table-column label="视频地址" align="center" prop="videoUrl" />
        <el-table-column label="视频识别范围" align="center" prop="videoIdentifyRange" />
        <el-table-column label="视频重点区域" align="center" prop="videoFocusArea" />
        <el-table-column label="雷达数据通道" align="center" prop="radarDataChannel">
          <template #default="scope">
            <dict-tag :options="utc_radar_data_channel" :value="scope.row.radarDataChannel"/>
          </template>
        </el-table-column>
        <el-table-column label="雷达数据服务" align="center" prop="radarDataService">
          <template #default="scope">
            <dict-tag :options="utc_radar_data_service" :value="scope.row.radarDataService"/>
          </template>
        </el-table-column>
        <el-table-column label="雷达斑马线位置" align="center" prop="radarCrossPosition" /> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['business:intersectionDevice:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                v-hasPermi="['business:intersectionDevice:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改路口设备对话框 -->
    <el-drawer :title="drawer.title" v-model="drawer.visible" size="50%" append-to-body>
      <el-form ref="intersectionDeviceFormRef" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" placeholder="请输入主键" />
        </el-form-item> -->
        <el-form-item label="所属路口" prop="deviceIntersectionId">
          <el-select v-model="form.deviceIntersectionId" placeholder="请选择路口">
            <el-option v-for="dict in intersectionList" :key="dict.id" :label="dict.intersectionName"
              :value="dict.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="连接方式" prop="connectType">
              <el-select v-model="form.connectType" placeholder="请选择连接方式">
                <el-option v-for="dict in utc_connect_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select v-model="form.deviceType" placeholder="请选择设备类型">
                <el-option v-for="dict in utc_intersection_device_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备厂家" prop="deviceVender">
              <el-select v-model="form.deviceVender" placeholder="请选择设备厂家">
                <el-option v-for="dict in utc_vender_name" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="安装位置" prop="installPosition">
              <el-select v-model="form.installPosition" placeholder="请选择安装位置">
                <el-option v-for="dict in utc_install_position" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="识别方向" prop="identifyDirection">
              <el-select v-model="form.identifyDirection" placeholder="请选择识别方向">
                <el-option v-for="dict in utc_identify_direction" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="设备IP" prop="deviceIp">
              <el-input v-model="form.deviceIp" placeholder="请输入设备IP" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备端口" prop="devicePort">
              <el-input-number :min="0" :max="65535" v-model="form.devicePort" style="width: 100%;"
                placeholder="请输入设备端口" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider v-if="form.deviceType === '3'" content-position="left">视频设备</el-divider>
        <el-form-item v-if="form.deviceType === '3'" label="视频地址" prop="videoUrl">
          <el-input v-model="form.videoUrl" placeholder="请输入视频地址" />
        </el-form-item>
        <el-form-item v-if="form.deviceType === '3'" label="识别范围" prop="videoIdentifyRange">
          <el-input v-model="form.videoIdentifyRange" placeholder="请输入视频识别范围" />
        </el-form-item>
        <el-form-item v-if="form.deviceType === '3'" label="重点区域" prop="videoFocusArea">
          <el-input v-model="form.videoFocusArea" placeholder="请输入视频重点区域" />
        </el-form-item>
        <el-divider v-if="form.deviceType === '1' || form.deviceType === '2'"
          content-position="left">雷达/雷视一体设备</el-divider>
        <el-form-item v-if="form.deviceType === '1' || form.deviceType === '2'" label="数据通道" prop="radarDataChannel">
          <el-select v-model="form.radarDataChannel" placeholder="请选择雷达数据通道">
            <el-option v-for="dict in utc_radar_data_channel" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.deviceType === '1' || form.deviceType === '2'" label="数据服务" prop="radarDataService">
          <el-select v-model="form.radarDataService" placeholder="请选择雷达数据服务">
            <el-option v-for="dict in utc_radar_data_service" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.deviceType === '1' || form.deviceType === '2'" label="斑马线位置" prop="radarCrossPosition">
          <el-input v-model="form.radarCrossPosition" placeholder="请输入雷达斑马线位置" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="drawer-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="IntersectionDevice" lang="ts">
import { listIntersectionDevice, getIntersectionDevice, delIntersectionDevice, addIntersectionDevice, updateIntersectionDevice } from '@/api/business/intersectionDevice';
import { listIntersectionDrop } from '@/api/business/intersectionInfo';
import { IntersectionDeviceVO, IntersectionDeviceQuery, IntersectionDeviceForm } from '@/api/business/intersectionDevice/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { utc_radar_data_service, utc_intersection_device_type, utc_radar_data_channel, utc_vender_name, utc_connect_type, utc_install_position, utc_identify_direction } = toRefs<any>(proxy?.useDict('utc_radar_data_service', 'utc_intersection_device_type', 'utc_radar_data_channel', 'utc_vender_name', 'utc_connect_type', 'utc_install_position', 'utc_identify_direction'));

const intersectionDeviceList = ref<IntersectionDeviceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const intersectionDeviceFormRef = ref<ElFormInstance>();

const drawer = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: IntersectionDeviceForm = {
  id: undefined,
  deviceCode: undefined,
  deviceName: undefined,
  deviceType: undefined,
  deviceVender: undefined,
  installPosition: undefined,
  identifyDirection: undefined,
  connectType: undefined,
  deviceIp: undefined,
  devicePort: undefined,
  videoUrl: undefined,
  videoIdentifyRange: undefined,
  videoFocusArea: undefined,
  radarDataChannel: undefined,
  radarDataService: undefined,
  radarCrossPosition: undefined,
  deviceIntersectionId: undefined,
}
const data = reactive<PageData<IntersectionDeviceForm, IntersectionDeviceQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deviceCode: undefined,
    deviceName: undefined,
    deviceType: undefined,
    deviceVender: undefined,
    deviceIntersectionId: undefined,
    params: {
    }
  },
  rules: {
    deviceIntersectionId: [
      { required: true, message: "所属路口不能为空", trigger: "blur" }
    ],
    deviceCode: [
      { required: true, message: "设备编码不能为空", trigger: "blur" }
    ],
    deviceName: [
      { required: true, message: "设备名称不能为空", trigger: "blur" }
    ],
    deviceType: [
      { required: true, message: "设备类型不能为空", trigger: "change" }
    ],
    deviceVender: [
      { required: true, message: "设备厂家不能为空", trigger: "change" }
    ],
    installPosition: [
      { required: true, message: "安装位置不能为空", trigger: "change" }
    ],
    identifyDirection: [
      { required: true, message: "识别方向不能为空", trigger: "change" }
    ],
    connectType: [
      { required: true, message: "连接方式不能为空", trigger: "change" }
    ],
    deviceIp: [
      { required: true, message: "设备IP不能为空", trigger: "blur" }
    ],
    devicePort: [
      { required: true, message: "设备端口不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

const intersectionList = ref([]);

/** 查询路口设备列表 */
const getList = async () => {
  loading.value = true;
  const res = await listIntersectionDevice(queryParams.value);
  intersectionDeviceList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  drawer.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  intersectionDeviceFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: IntersectionDeviceVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  drawer.visible = true;
  drawer.title = "添加路口设备";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: IntersectionDeviceVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getIntersectionDevice(_id);
  Object.assign(form.value, res.data);
  drawer.visible = true;
  drawer.title = "修改路口设备";
}

/** 提交按钮 */
const submitForm = () => {
  intersectionDeviceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateIntersectionDevice(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addIntersectionDevice(form.value).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      drawer.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: IntersectionDeviceVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除路口设备编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delIntersectionDevice(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('business/intersectionDevice/export', {
    ...queryParams.value
  }, `intersectionDevice_${new Date().getTime()}.xlsx`)
}

const setupIntersectionDrop = async () => {
  const res = await listIntersectionDrop();
  intersectionList.value = res.data;
}

onMounted(() => {
  getList();
  setupIntersectionDrop();
});
</script>
